#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
机器码验证系统 - 生成器
用于生成和管理用户机器的验证码

功能包括：
1. 获取机器硬件信息
2. 生成唯一机器码
3. 生成对应的激活码
4. 管理激活码数据库
"""

import os
import sys
import json
import hashlib
import hmac
import base64
import platform
import uuid
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
import sqlite3


class MachineCodeGenerator:
    """机器码生成器"""
    
    def __init__(self, db_path="license_db.sqlite"):
        self.db_path = db_path
        self.secret_key = "FanqieBookUpload2024SecretKey"  # 服务端密钥
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建许可证表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS licenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                machine_code TEXT UNIQUE NOT NULL,
                license_key TEXT UNIQUE NOT NULL,
                user_name TEXT,
                email TEXT,
                created_time TEXT NOT NULL,
                expire_time TEXT,
                max_devices INTEGER DEFAULT 1,
                is_active BOOLEAN DEFAULT 1,
                last_check_time TEXT,
                notes TEXT
            )
        ''')
        
        # 创建设备使用记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS device_usage (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_key TEXT NOT NULL,
                machine_code TEXT NOT NULL,
                ip_address TEXT,
                last_used_time TEXT NOT NULL,
                usage_count INTEGER DEFAULT 1,
                FOREIGN KEY (license_key) REFERENCES licenses (license_key)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def get_machine_info(self):
        """获取机器硬件信息"""
        try:
            info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'machine': platform.machine(),
                'node': platform.node(),
                'system': platform.system(),
                'version': platform.version(),
                'mac_address': ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                                        for elements in range(0, 2*6, 2)][::-1]),
            }
            
            # Windows系统获取更多信息
            if platform.system() == "Windows":
                try:
                    # 获取主板序列号
                    result = subprocess.run(['wmic', 'baseboard', 'get', 'serialnumber'], 
                                          capture_output=True, text=True)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        if len(lines) > 1:
                            info['motherboard_serial'] = lines[1].strip()
                    
                    # 获取硬盘序列号
                    result = subprocess.run(['wmic', 'diskdrive', 'get', 'serialnumber'], 
                                          capture_output=True, text=True)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        if len(lines) > 1:
                            info['disk_serial'] = lines[1].strip()
                            
                    # 获取CPU信息
                    result = subprocess.run(['wmic', 'cpu', 'get', 'processorid'], 
                                          capture_output=True, text=True)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        if len(lines) > 1:
                            info['cpu_id'] = lines[1].strip()
                except:
                    pass
            
            return info
            
        except Exception as e:
            print(f"获取机器信息失败: {e}")
            return None
    
    def generate_machine_code(self, machine_info=None):
        """生成机器码"""
        if machine_info is None:
            machine_info = self.get_machine_info()
        
        if not machine_info:
            return None
        
        # 将机器信息转换为字符串
        info_str = json.dumps(machine_info, sort_keys=True)
        
        # 生成机器码（使用SHA256）
        machine_code = hashlib.sha256(info_str.encode('utf-8')).hexdigest()[:16].upper()
        
        return machine_code
    
    def generate_license_key(self, machine_code, expire_days=365):
        """生成许可证密钥"""
        # 生成过期时间
        expire_time = datetime.now() + timedelta(days=expire_days)
        expire_str = expire_time.strftime("%Y%m%d")
        
        # 组合数据
        data = f"{machine_code}:{expire_str}"
        
        # 使用HMAC-SHA256生成签名
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            data.encode('utf-8'),
            hashlib.sha256
        ).digest()
        
        # Base64编码
        license_key = base64.b64encode(signature).decode('utf-8')[:20]
        
        return license_key
    
    def create_license(self, user_name="", email="", expire_days=365, max_devices=1, notes=""):
        """创建新的许可证（使用当前机器）"""
        # 获取机器信息
        machine_info = self.get_machine_info()
        if not machine_info:
            return None, "无法获取机器信息"
        
        # 生成机器码
        machine_code = self.generate_machine_code(machine_info)
        if not machine_code:
            return None, "无法生成机器码"
        
        return self._create_license_internal(machine_code, user_name, email, expire_days, max_devices, notes, machine_info)
    
    def create_license_for_machine(self, machine_code, user_name="", email="", expire_days=365, max_devices=1, notes=""):
        """为指定机器码创建许可证"""
        if not machine_code:
            return None, "机器码不能为空"
        
        # 验证机器码格式
        if len(machine_code) != 16:
            return None, "机器码格式错误，必须是16位字符"
        
        try:
            int(machine_code, 16)  # 验证是否为有效的十六进制
        except ValueError:
            return None, "机器码必须是有效的十六进制字符"
        
        return self._create_license_internal(machine_code, user_name, email, expire_days, max_devices, notes)
    
    def _create_license_internal(self, machine_code, user_name="", email="", expire_days=365, max_devices=1, notes="", machine_info=None):
        """内部方法：创建许可证的核心逻辑"""
        # 生成许可证密钥
        license_key = self.generate_license_key(machine_code, expire_days)
        
        # 保存到数据库
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            expire_time = (datetime.now() + timedelta(days=expire_days)).strftime("%Y-%m-%d %H:%M:%S")
            
            cursor.execute('''
                INSERT INTO licenses 
                (machine_code, license_key, user_name, email, created_time, expire_time, max_devices, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (machine_code, license_key, user_name, email, current_time, expire_time, max_devices, notes))
            
            conn.commit()
            conn.close()
            
            # 创建许可证文件
            self._create_license_file(machine_code, license_key, user_name, email, current_time, expire_time, max_devices, notes)
            
            result = {
                'machine_code': machine_code,
                'license_key': license_key,
                'user_name': user_name,
                'email': email,
                'expire_time': expire_time,
                'max_devices': max_devices
            }
            
            if machine_info:
                result['machine_info'] = machine_info
                
            return result, "许可证创建成功"
            
        except sqlite3.IntegrityError as e:
            if "machine_code" in str(e):
                return None, f"机器码 {machine_code} 已存在许可证"
            elif "license_key" in str(e):
                return None, "许可证密钥冲突，请重试"
            else:
                return None, f"数据库错误: {e}"
        except Exception as e:
            return None, f"创建许可证失败: {e}"
    
    def _create_license_file(self, machine_code, license_key, user_name, email, created_time, expire_time, max_devices, notes):
        """创建许可证文件"""
        try:
            filename = f"license_{machine_code}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("番茄小说上传工具 - 许可证信息\n")
                f.write("=" * 40 + "\n")
                f.write(f"用户名: {user_name}\n")
                f.write(f"邮箱: {email}\n")
                f.write(f"机器码: {machine_code}\n")
                f.write(f"许可证密钥: {license_key}\n")
                f.write(f"创建时间: {created_time}\n")
                f.write(f"过期时间: {expire_time}\n")
                f.write(f"最大设备数: {max_devices}\n")
                f.write(f"备注: {notes}\n")
                f.write("\n" + "=" * 40 + "\n")
                f.write("使用说明：\n")
                f.write("1. 请将上述机器码和许可证密钥提供给用户\n")
                f.write("2. 用户需要在软件中输入许可证密钥进行激活\n")
                f.write("3. 激活成功后软件将正常使用\n")
                f.write("4. 请妥善保管此许可证信息\n")
        except Exception as e:
            print(f"创建许可证文件失败: {e}")
    
    def verify_license(self, license_key, machine_code):
        """验证许可证"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM licenses 
                WHERE license_key = ? AND machine_code = ? AND is_active = 1
            ''', (license_key, machine_code))
            
            result = cursor.fetchone()
            conn.close()
            
            if not result:
                return False, "许可证无效或已停用"
            
            # 检查过期时间
            expire_time = datetime.strptime(result[5], "%Y-%m-%d %H:%M:%S")
            if datetime.now() > expire_time:
                return False, "许可证已过期"
            
            return True, "许可证验证成功"
            
        except Exception as e:
            return False, f"验证失败: {e}"
    
    def list_licenses(self):
        """列出所有许可证"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM licenses ORDER BY created_time DESC')
            results = cursor.fetchall()
            conn.close()
            
            licenses = []
            for row in results:
                licenses.append({
                    'id': row[0],
                    'machine_code': row[1],
                    'license_key': row[2],
                    'user_name': row[3],
                    'email': row[4],
                    'created_time': row[5],
                    'expire_time': row[6],
                    'max_devices': row[7],
                    'is_active': bool(row[8]),
                    'last_check_time': row[9],
                    'notes': row[10]
                })
            
            return licenses
            
        except Exception as e:
            print(f"获取许可证列表失败: {e}")
            return []
    
    def deactivate_license(self, license_key):
        """停用许可证"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE licenses SET is_active = 0 WHERE license_key = ?
            ''', (license_key,))
            
            conn.commit()
            conn.close()
            
            return cursor.rowcount > 0
            
        except Exception as e:
            print(f"停用许可证失败: {e}")
            return False


def main():
    """主函数 - 命令行界面"""
    generator = MachineCodeGenerator()
    
    print("=" * 60)
    print("            番茄小说上传工具 - 机器码生成器")
    print("=" * 60)
    
    while True:
        print("\n请选择操作：")
        print("1. 获取当前机器信息")
        print("2. 生成机器码")
        print("3. 创建新许可证")
        print("4. 验证许可证")
        print("5. 查看所有许可证")
        print("6. 停用许可证")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-6): ").strip()
        
        if choice == "0":
            print("再见！")
            break
        elif choice == "1":
            print("\n正在获取机器信息...")
            machine_info = generator.get_machine_info()
            if machine_info:
                print("\n机器信息：")
                for key, value in machine_info.items():
                    print(f"  {key}: {value}")
            else:
                print("获取机器信息失败！")
                
        elif choice == "2":
            print("\n正在生成机器码...")
            machine_code = generator.generate_machine_code()
            if machine_code:
                print(f"\n机器码: {machine_code}")
            else:
                print("生成机器码失败！")
                
        elif choice == "3":
            print("\n创建新许可证")
            user_name = input("用户名 (可选): ").strip()
            email = input("邮箱 (可选): ").strip()
            
            try:
                expire_days = int(input("有效期天数 (默认365): ").strip() or "365")
                max_devices = int(input("最大设备数 (默认1): ").strip() or "1")
            except ValueError:
                expire_days = 365
                max_devices = 1
            
            notes = input("备注 (可选): ").strip()
            
            result, message = generator.create_license(user_name, email, expire_days, max_devices, notes)
            if result:
                print(f"\n✅ {message}")
                print(f"机器码: {result['machine_code']}")
                print(f"许可证密钥: {result['license_key']}")
                print(f"过期时间: {result['expire_time']}")
                
                # 保存到文件
                filename = f"license_{result['machine_code']}.txt"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"番茄小说上传工具 - 许可证信息\n")
                    f.write(f"=" * 40 + "\n")
                    f.write(f"用户名: {user_name}\n")
                    f.write(f"邮箱: {email}\n")
                    f.write(f"机器码: {result['machine_code']}\n")
                    f.write(f"许可证密钥: {result['license_key']}\n")
                    f.write(f"创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"过期时间: {result['expire_time']}\n")
                    f.write(f"最大设备数: {max_devices}\n")
                    f.write(f"备注: {notes}\n")
                
                print(f"\n许可证信息已保存到: {filename}")
            else:
                print(f"\n❌ {message}")
                
        elif choice == "4":
            print("\n验证许可证")
            license_key = input("请输入许可证密钥: ").strip()
            machine_code = input("请输入机器码 (留空使用当前机器): ").strip()
            
            if not machine_code:
                machine_code = generator.generate_machine_code()
                print(f"当前机器码: {machine_code}")
            
            is_valid, message = generator.verify_license(license_key, machine_code)
            if is_valid:
                print(f"\n✅ {message}")
            else:
                print(f"\n❌ {message}")
                
        elif choice == "5":
            print("\n所有许可证：")
            licenses = generator.list_licenses()
            if licenses:
                print(f"{'ID':<5} {'用户名':<15} {'机器码':<20} {'许可证密钥':<25} {'状态':<8} {'过期时间'}")
                print("-" * 100)
                for license in licenses:
                    status = "有效" if license['is_active'] else "停用"
                    print(f"{license['id']:<5} {license['user_name']:<15} {license['machine_code']:<20} "
                          f"{license['license_key']:<25} {status:<8} {license['expire_time']}")
            else:
                print("暂无许可证记录")
                
        elif choice == "6":
            print("\n停用许可证")
            license_key = input("请输入要停用的许可证密钥: ").strip()
            
            if generator.deactivate_license(license_key):
                print("✅ 许可证已停用")
            else:
                print("❌ 停用失败，请检查许可证密钥")
        else:
            print("无效选择，请重试")


if __name__ == "__main__":
    main()
