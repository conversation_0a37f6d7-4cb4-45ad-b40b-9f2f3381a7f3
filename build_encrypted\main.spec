# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('drivers', 'drivers'),
        ('config.json', '.'),
        ('src', 'src'),
    ],
    hiddenimports=[
        'PySide6.QtCore',
        'PySide6.QtWidgets', 
        'PySide6.QtGui',
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.chrome',
        'selenium.webdriver.chrome.service',
        'selenium.webdriver.chrome.options',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support.expected_conditions',
        'selenium.common.exceptions',
        'requests',
        'requests.adapters',
        'requests.auth',
        'requests.cookies',
        'requests.exceptions',
        'requests.models',
        'requests.sessions',
        'requests.structures',
        'requests.utils',
        'urllib3',
        'urllib3.poolmanager',
        'urllib3.util',
        'urllib3.util.retry',
        'json',
        'time',
        'os',
        'sys',
        'pathlib',
        'zipfile',
        'tempfile',
        'shutil',
        'platform',
        'configparser',
        'src',
        'src.core',
        'src.core.fanqie_uploader',
        'src.core.novel_parser',
        'src.core.webdriver_manager',
        'src.core.config_manager',
        'src.ui',
        'src.ui.main_window',
        'src.ui.book_info_widget',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='FanqieBookUpload',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # 禁用UPX压缩，避免某些杀毒软件误报
    upx_exclude=[],
    console=False,  # 设置为False创建GUI应用
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='Z:\Python\FanqieBookUpload\logo.ico',  # 动态设置图标路径
)
