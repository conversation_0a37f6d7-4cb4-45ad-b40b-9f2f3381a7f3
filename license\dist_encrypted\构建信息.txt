许可证管理系统 - 构建信息
===============================

构建时间: 2025-07-08 10:02:36
构建版本: v2.0.1 (终端窗口优化版)
加密工具: PyArmor
打包工具: PyInstaller

已加密的文件:
  - machine_code_generator.py
  - ultimate_no_console.py
  - create_test_license.py
  - demo_license_system.py

包含的资源:
  - 机器码验证系统使用说明.md
  - license_db.sqlite
  - logo.ico
  - config.ini
  - settings.json

启动方式说明:
1. 直接启动: 双击"许可证管理器.exe"
2. 批处理启动: 双击"启动许可证管理器.bat"
3. 静默启动: 双击"静默启动许可证管理器.vbs" (强烈推荐)

版本优化内容:
✅ 完全禁用控制台窗口弹出
✅ 优化PyInstaller配置参数
✅ 增强引导程序信号处理
✅ 禁用窗口化回溯
✅ 禁用参数模拟
✅ 提供VBS静默启动脚本

解决的问题:
- 反复弹出终端窗口
- 启动时控制台闪现
- GUI应用显示命令行

推荐使用方式:
建议用户使用"静默启动许可证管理器.vbs"脚本启动程序，
这样可以确保完全不显示任何命令行窗口，直接打开GUI界面。

注意事项:
- 请保持文件完整性，不要删除任何文件
- 数据库文件包含重要的许可证信息
- 建议定期备份整个目录
- VBS脚本提供最佳的用户体验

技术支持:
如有问题，请联系开发团队
