# PyArmor无控制台打包说明

## 概述

`build_no_console.py` 是一个集成了 PyArmor 代码加密和无控制台打包功能的脚本，专门解决以下问题：

1. **代码保护**: 使用 PyArmor 加密 Python 源代码
2. **无控制台**: 打包后的 exe 不会弹出黑框框
3. **单文件分发**: 生成单个 exe 文件，便于分发

## 功能特性

### ✅ 代码加密保护
- 使用 PyArmor 9.x 加密核心模块
- 保护商业逻辑和敏感代码
- 防止反编译和代码泄露

### ✅ 无控制台窗口
- 修复了所有 subprocess 调用的黑框框问题
- 打包后的 exe 启动时不会弹出命令行窗口
- 提供流畅的用户体验

### ✅ 自动化构建流程
- 一键完成加密、打包、资源复制
- 自动处理 PyArmor 的嵌套目录问题
- 智能清理临时文件

## 环境要求

### 必需依赖
```bash
pip install pyarmor>=9.0.0
pip install pyinstaller>=5.13.0
pip install PySide6>=6.5.0
```

### 系统要求
- Windows 10/11 (64位)
- Python 3.8+
- 至少 2GB 可用磁盘空间

## 使用方法

### 1. 环境检查
在运行打包脚本之前，建议先检查环境：

```bash
# 检查 PyArmor
pyarmor --version

# 检查 PyInstaller
python -c "import PyInstaller; print(PyInstaller.__version__)"

# 检查项目文件
ls main.py src/ config.json logo.ico
```

### 2. 执行打包
```bash
python build_no_console.py
```

### 3. 打包过程
脚本会自动执行以下步骤：

1. **🔍 检查依赖工具** - 验证 PyArmor 和 PyInstaller
2. **🧹 清理构建目录** - 删除旧的构建文件
3. **🔐 加密模块** - 使用 PyArmor 加密核心代码
4. **🔧 修复目录结构** - 处理 PyArmor 的嵌套目录
5. **📁 复制资源文件** - 复制配置文件和资源
6. **📄 创建 Spec 文件** - 生成 PyInstaller 配置
7. **🔨 构建 exe** - 使用 PyInstaller 打包
8. **🔍 验证构建结果** - 检查生成的文件
9. **📜 创建启动脚本** - 生成便捷启动脚本
10. **🧹 清理临时文件** - 删除临时加密目录

## 输出文件

打包完成后，在 `dist_no_console/` 目录下会生成：

```
dist_no_console/
├── FanqieBookUpload.exe    # 主程序（加密+无控制台）
└── 启动程序.bat            # 启动脚本
```

## 加密的模块

以下模块会被 PyArmor 加密：

- `src/core/` - 核心业务逻辑
- `src/ui/` - 用户界面代码
- `src/__init__.py` - 包初始化文件
- `main.py` - 主程序入口

## 技术细节

### PyArmor 配置
```python
# 加密命令示例
pyarmor gen -O output_dir -r --platform windows.x86_64 src/core/
```

### PyInstaller 配置
```python
# 关键配置
console=False,          # 无控制台窗口
upx=False,             # 禁用UPX压缩
debug=False,           # 禁用调试模式
strip=False,           # 保留符号信息
```

### subprocess 修复
```python
# 隐藏命令行窗口的参数
startupinfo = subprocess.STARTUPINFO()
startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
startupinfo.wShowWindow = subprocess.SW_HIDE
creationflags = subprocess.CREATE_NO_WINDOW
```

## 故障排除

### 常见问题

#### 1. PyArmor 许可证问题
```
错误: License verification failed
解决: 确保 PyArmor 许可证有效，或使用免费版本
```

#### 2. 模块导入错误
```
错误: ModuleNotFoundError
解决: 检查 hiddenimports 配置，添加缺失的模块
```

#### 3. 文件路径问题
```
错误: FileNotFoundError
解决: 确保所有资源文件存在，检查路径配置
```

#### 4. 构建超时
```
错误: subprocess.TimeoutExpired
解决: 增加超时时间，或检查系统资源
```

### 调试方法

#### 1. 启用详细输出
修改脚本中的 `capture_output=False` 来查看详细输出

#### 2. 保留临时文件
注释掉 `cleanup_temp_files()` 调用来保留临时文件

#### 3. 分步执行
可以单独运行加密或打包步骤进行调试

## 性能优化

### 构建时间优化
- 使用 SSD 硬盘
- 关闭实时杀毒软件
- 增加系统内存

### 文件大小优化
- 排除不必要的模块
- 使用 `--exclude-module` 参数
- 优化资源文件大小

## 安全注意事项

1. **加密密钥**: PyArmor 会生成运行时密钥，请妥善保管
2. **代码混淆**: 加密后的代码仍可能被分析，建议结合其他保护措施
3. **许可证管理**: 确保 PyArmor 许可证的合规使用

## 版本兼容性

- **PyArmor**: 支持 9.x 版本
- **PyInstaller**: 支持 5.x 版本
- **Python**: 支持 3.8-3.11

## 更新日志

### v1.0.0
- 集成 PyArmor 加密功能
- 修复无控制台窗口问题
- 自动化构建流程
- 智能错误处理和清理

## 技术支持

如遇到问题，请提供以下信息：
- Python 版本
- PyArmor 版本
- PyInstaller 版本
- 完整的错误日志
- 系统环境信息

这个打包脚本提供了企业级的代码保护和用户体验，适合商业软件的分发需求。
