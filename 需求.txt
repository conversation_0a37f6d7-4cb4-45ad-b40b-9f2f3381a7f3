现在要基于番茄小说作家工作台网页https://fanqienovel.com 开发一个自动根据固定格式的 txt 小说稿，按章节进行上传发布的脚本，使用 Python 开发：

1. 我会导入固定格式的小说稿 txt 文件，文件内容格式如下： 
#**章节标题**#
   章节正文 
#**章节标题**#
   章节正文
2. 自动以发布到番茄小说作家工作台 的草稿箱中 即可
3. 需要有登录的 cookie 信息，可以通过浏览器开发者工具获取
4. 需要提供简单的 pyside6 的 GUI 界面，用户导入小说稿 txt 文件、自动罗列出小说的章节标题、对应章节字数，上传状态，上传进度等信息
5. 输入用户 Cookie 信息，输入小说书本 ID 号,
6. 需要日志显示框，显示脚本运行过程中产生的日志信息。
7. 点击上传，脚本会自动按章节上传到该小说的草稿箱中。

上传保存草稿箱的API路由
/api/author/article/cover_article/v0/?msToken=K7m2V1L7DJE9glK-SJIYf3Jh6pd11kcXfqvoIT2VVxu8T-fOSVpWerwbTbJIqxMUz3Bfp9SZmobdNwO6N6LcsUe8YqTaS1M-yF_7QOTTz5isfFB3yLF7rW3B2MFMUrQP65Gp68uzH5J2HRsxbo_pRpLIHZzMEKox3b7Odx3aAvZMNQ%3D%3D&a_bogus=xysRhHyjQqWfFpASmKDWHRel0%2F6%2FNTWy5Pi%2FSjfCyxORGXtPQcB6kuROcFzN%2FDsq0mpfhF9H9jGQOfxOOSReIo3poshDSYJbB02C9X0o0qqkGl4QEN8mShUzLwMFUQvNlACbiI85AsMxIVVRnqVYAQZGt5zo5cfgRqBCpZG9JDS03BgTI9Q9eVtWV7f%3D
参数：
aid=2503&app_name=muye_novel&book_id=7508306478608960536&item_id=7522743947639144985&title=%E7%AC%AC1%E7%AB%A0%20aaaaaa&content=%3Cp%3E111111%3C%2Fp%3E%3Cp%3E11111%3C%2Fp%3E%3Cp%3E11111%3C%2Fp%3E%3Cp%3E%3C%2Fp%3E&volume_name=%E7%AC%AC%E4%B8%80%E5%8D%B7%EF%BC%9A%E9%BB%98%E8%AE%A4&volume_id=7508306481888889880



优化弹窗检测处理逻辑， 则界面加一个弹窗状态显示（默认为 "无弹窗"），
打开发布页面，如果检测到一个弹窗，状态则改为 “有弹窗”，
状态为有弹窗时，则不执行章节内容解析发布，此时需要弹窗提示“人工手动先去逐个完成五个步骤的弹窗关闭”，
关闭完成后，再到软件界面，“继续上传”，人工激活后续文章解析发布逻辑后再开始正式解析内容，填充内容，并发布