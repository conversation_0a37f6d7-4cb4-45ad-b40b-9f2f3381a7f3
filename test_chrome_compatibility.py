#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Chrome浏览器兼容性测试脚本
用于测试Chrome版本检测和ChromeDriver自动下载功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_chrome_detection():
    """测试Chrome版本检测"""
    print("=" * 60)
    print("测试Chrome版本检测")
    print("=" * 60)
    
    try:
        from src.core.webdriver_manager import WebDriverManager
        
        wm = WebDriverManager()
        chrome_version = wm.get_chrome_version()
        
        if chrome_version:
            print(f"✓ 成功检测到Chrome版本: {chrome_version}")
            major_version = chrome_version.split('.')[0]
            print(f"✓ Chrome主版本号: {major_version}")
            return chrome_version
        else:
            print("✗ 无法检测到Chrome版本")
            return None
            
    except Exception as e:
        print(f"✗ Chrome版本检测失败: {e}")
        return None

def test_chromedriver_version_matching(chrome_version):
    """测试ChromeDriver版本匹配"""
    print("\n" + "=" * 60)
    print("测试ChromeDriver版本匹配")
    print("=" * 60)
    
    if not chrome_version:
        print("✗ 跳过测试：没有Chrome版本信息")
        return None
    
    try:
        from src.core.webdriver_manager import WebDriverManager
        
        wm = WebDriverManager()
        driver_version = wm.get_chromedriver_version(chrome_version)
        
        if driver_version:
            print(f"✓ 匹配的ChromeDriver版本: {driver_version}")
            return driver_version
        else:
            print("✗ 无法获取匹配的ChromeDriver版本")
            return None
            
    except Exception as e:
        print(f"✗ ChromeDriver版本匹配失败: {e}")
        return None

def test_chromedriver_download(driver_version):
    """测试ChromeDriver下载"""
    print("\n" + "=" * 60)
    print("测试ChromeDriver下载")
    print("=" * 60)
    
    if not driver_version:
        print("✗ 跳过测试：没有ChromeDriver版本信息")
        return None
    
    try:
        from src.core.webdriver_manager import WebDriverManager
        
        wm = WebDriverManager()
        
        # 检查是否已存在
        chromedriver_name = "chromedriver.exe" if wm.system == "windows" else "chromedriver"
        local_path = wm.drivers_dir / chromedriver_name
        
        if local_path.exists():
            print(f"✓ 发现现有ChromeDriver: {local_path}")
            
            # 检查兼容性
            chrome_version = wm.get_chrome_version()
            if chrome_version and wm.check_chromedriver_compatibility(str(local_path), chrome_version):
                print("✓ 现有ChromeDriver版本兼容")
                return str(local_path)
            else:
                print("! 现有ChromeDriver版本不兼容，需要更新")
        
        print("开始下载新的ChromeDriver...")
        downloaded_path = wm.download_chromedriver(driver_version)
        
        if downloaded_path:
            print(f"✓ ChromeDriver下载成功: {downloaded_path}")
            return downloaded_path
        else:
            print("✗ ChromeDriver下载失败")
            return None
            
    except Exception as e:
        print(f"✗ ChromeDriver下载测试失败: {e}")
        return None

def test_webdriver_initialization(chromedriver_path):
    """测试WebDriver初始化"""
    print("\n" + "=" * 60)
    print("测试WebDriver初始化")
    print("=" * 60)
    
    if not chromedriver_path:
        print("✗ 跳过测试：没有ChromeDriver路径")
        return False
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        
        print(f"使用ChromeDriver路径: {chromedriver_path}")
        
        # 设置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 无头模式，不显示浏览器窗口
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        
        # 创建服务
        service = Service(executable_path=chromedriver_path)
        
        # 创建WebDriver实例
        print("正在初始化WebDriver...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 测试基本功能
        print("测试基本功能...")
        driver.get("https://www.google.com")
        title = driver.title
        print(f"✓ 页面标题: {title}")
        
        # 清理
        driver.quit()
        print("✓ WebDriver初始化测试成功")
        return True
        
    except Exception as e:
        print(f"✗ WebDriver初始化失败: {e}")
        return False

def test_full_integration():
    """完整集成测试"""
    print("\n" + "=" * 60)
    print("完整集成测试")
    print("=" * 60)
    
    try:
        from src.core.webdriver_manager import WebDriverManager
        
        wm = WebDriverManager()
        
        # 强制更新测试
        print("测试强制更新功能...")
        chromedriver_path = wm.get_chromedriver_path(force_update=False)
        
        if chromedriver_path:
            print(f"✓ 集成测试成功: {chromedriver_path}")
            return True
        else:
            print("✗ 集成测试失败")
            return False
            
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Chrome浏览器兼容性测试")
    print("=" * 60)
    
    # 测试Chrome版本检测
    chrome_version = test_chrome_detection()
    
    # 测试ChromeDriver版本匹配
    driver_version = test_chromedriver_version_matching(chrome_version)
    
    # 测试ChromeDriver下载
    chromedriver_path = test_chromedriver_download(driver_version)
    
    # 测试WebDriver初始化
    webdriver_success = test_webdriver_initialization(chromedriver_path)
    
    # 完整集成测试
    integration_success = test_full_integration()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    results = [
        ("Chrome版本检测", chrome_version is not None),
        ("ChromeDriver版本匹配", driver_version is not None),
        ("ChromeDriver下载", chromedriver_path is not None),
        ("WebDriver初始化", webdriver_success),
        ("完整集成测试", integration_success)
    ]
    
    all_passed = True
    for test_name, passed in results:
        status = "✓ 通过" if passed else "✗ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！Chrome浏览器兼容性问题已解决。")
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
    print("=" * 60)
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生未预期的错误: {e}")
        sys.exit(1)
