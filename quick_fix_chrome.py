#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Chrome兼容性快速修复脚本
直接下载Chrome 139对应的ChromeDriver
"""

import os
import sys
import time
import zipfile
import requests
from pathlib import Path

def download_file(url, local_path, description="文件"):
    """下载文件并显示进度"""
    try:
        print(f"正在下载{description}...")
        print(f"下载地址: {url}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, stream=True, timeout=30)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r下载进度: {progress:.1f}% ({downloaded}/{total_size} 字节)", end='', flush=True)
        
        print(f"\n{description}下载完成: {local_path}")
        return True
        
    except Exception as e:
        print(f"\n下载{description}失败: {e}")
        return False

def extract_chromedriver(zip_path, extract_dir):
    """解压ChromeDriver"""
    try:
        print("正在解压ChromeDriver...")
        
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_dir)
        
        # 查找chromedriver.exe文件
        chromedriver_path = None
        for root, dirs, files in os.walk(extract_dir):
            for file in files:
                if file.startswith('chromedriver') and file.endswith('.exe'):
                    chromedriver_path = os.path.join(root, file)
                    break
            if chromedriver_path:
                break
        
        if chromedriver_path:
            print(f"找到ChromeDriver: {chromedriver_path}")
            return chromedriver_path
        else:
            print("解压后未找到chromedriver.exe文件")
            return None
            
    except Exception as e:
        print(f"解压失败: {e}")
        return None

def backup_existing_chromedriver(drivers_dir):
    """备份现有的ChromeDriver"""
    existing_path = drivers_dir / "chromedriver.exe"
    if existing_path.exists():
        backup_path = drivers_dir / f"chromedriver_backup_{int(time.time())}.exe"
        try:
            os.rename(existing_path, backup_path)
            print(f"已备份现有ChromeDriver到: {backup_path}")
            return True
        except Exception as e:
            print(f"备份失败: {e}")
            return False
    return True

def verify_chromedriver(chromedriver_path):
    """验证ChromeDriver是否正常工作"""
    try:
        import subprocess
        
        result = subprocess.run([
            chromedriver_path, '--version'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and result.stdout:
            version_info = result.stdout.strip()
            print(f"ChromeDriver验证成功: {version_info}")
            return True
        else:
            print("ChromeDriver验证失败")
            return False
            
    except Exception as e:
        print(f"验证ChromeDriver时出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("Chrome 139兼容性快速修复工具")
    print("=" * 60)
    
    # 设置路径
    base_dir = Path(__file__).parent
    drivers_dir = base_dir / "drivers"
    drivers_dir.mkdir(exist_ok=True)
    
    # Chrome 139对应的ChromeDriver版本和下载地址
    chromedriver_version = "139.0.7258.66"
    download_urls = [
        f"https://storage.googleapis.com/chrome-for-testing-public/{chromedriver_version}/win64/chromedriver-win64.zip",
        f"https://edgedl.me.gvt1.com/edgedl/chrome/chrome-for-testing/{chromedriver_version}/win64/chromedriver-win64.zip"
    ]
    
    print(f"目标ChromeDriver版本: {chromedriver_version}")
    print(f"安装目录: {drivers_dir}")
    
    # 备份现有版本
    print("\n1. 备份现有ChromeDriver...")
    if not backup_existing_chromedriver(drivers_dir):
        print("备份失败，但继续执行...")
    
    # 尝试下载
    print("\n2. 下载新的ChromeDriver...")
    zip_path = drivers_dir / f"chromedriver_{chromedriver_version}_win64.zip"
    
    download_success = False
    for i, url in enumerate(download_urls):
        print(f"\n尝试下载地址 {i+1}/{len(download_urls)}")
        if download_file(url, zip_path, "ChromeDriver"):
            download_success = True
            break
        else:
            print(f"下载地址 {i+1} 失败，尝试下一个...")
    
    if not download_success:
        print("\n❌ 所有下载地址都失败了")
        print("\n手动下载方案:")
        print("1. 访问: https://googlechromelabs.github.io/chrome-for-testing/")
        print("2. 下载Chrome 139对应的ChromeDriver")
        print("3. 解压并将chromedriver.exe放入drivers目录")
        return False
    
    # 解压文件
    print("\n3. 解压ChromeDriver...")
    extracted_path = extract_chromedriver(zip_path, drivers_dir)
    
    if not extracted_path:
        print("❌ 解压失败")
        return False
    
    # 移动到正确位置
    print("\n4. 安装ChromeDriver...")
    final_path = drivers_dir / "chromedriver.exe"
    
    try:
        if extracted_path != str(final_path):
            if final_path.exists():
                final_path.unlink()  # 删除现有文件
            
            os.rename(extracted_path, final_path)
            print(f"ChromeDriver已安装到: {final_path}")
        
        # 清理临时文件
        if zip_path.exists():
            zip_path.unlink()
            print("已清理临时文件")
        
        # 清理解压目录
        for item in drivers_dir.iterdir():
            if item.is_dir() and "chromedriver" in item.name.lower():
                import shutil
                shutil.rmtree(item)
                print(f"已清理临时目录: {item}")
        
    except Exception as e:
        print(f"安装失败: {e}")
        return False
    
    # 验证安装
    print("\n5. 验证安装...")
    if verify_chromedriver(str(final_path)):
        print("\n🎉 ChromeDriver修复成功！")
        print(f"Chrome 139现在可以正常使用了")
        print(f"ChromeDriver路径: {final_path}")
        return True
    else:
        print("\n❌ ChromeDriver验证失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        
        print("\n" + "=" * 60)
        if success:
            print("✅ 修复完成！现在可以正常启动程序了。")
        else:
            print("❌ 修复失败！请尝试手动下载或联系技术支持。")
        print("=" * 60)
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n修复被用户中断")
    except Exception as e:
        print(f"\n修复过程中发生错误: {e}")
        input("\n按回车键退出...")
