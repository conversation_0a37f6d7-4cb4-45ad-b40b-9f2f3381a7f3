# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['Z:\\Python\\FanqieBookUpload\\main.py'],
    pathex=[],
    binaries=[],
    datas=[('drivers', 'drivers'), ('config.json', '.'), ('src', 'src')],
    hiddenimports=['PySide6.QtCore', 'PySide6.QtWidgets', 'PySide6.QtGui', 'selenium', 'selenium.webdriver', 'selenium.webdriver.chrome', 'selenium.webdriver.chrome.service', 'selenium.webdriver.chrome.options', 'selenium.webdriver.common.by', 'selenium.webdriver.support.ui', 'selenium.webdriver.support.expected_conditions', 'selenium.common.exceptions', 'src', 'src.core', 'src.core.fanqie_uploader', 'src.core.novel_parser', 'src.core.webdriver_manager', 'src.core.config_manager', 'src.ui', 'src.ui.main_window', 'src.ui.book_info_widget'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='FanqieBookUpload',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['Z:\\Python\\FanqieBookUpload\\logo.ico'],
)
