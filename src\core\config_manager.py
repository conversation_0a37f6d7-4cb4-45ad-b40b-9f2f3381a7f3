"""
配置管理器 - 处理Cookie和书本ID的保存与读取
"""
import json
import os
from typing import Dict, Optional, Tuple
from datetime import datetime

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self) -> Dict:
        """
        加载配置文件
        
        Returns:
            Dict: 配置信息
        """
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"配置文件加载成功: {self.config_file}")
                return config
            except Exception as e:
                print(f"配置文件加载失败: {e}")
                return self._get_default_config()
        else:
            print("配置文件不存在，使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """
        获取默认配置
        
        Returns:
            Dict: 默认配置信息
        """
        return {
            "cookie": "",
            "book_id": "",
            "last_updated": "",
            "book_info": {
                "title": "",
                "cover_url": "",
                "target_audience": "",
                "tags": "",
                "main_character": "",
                "description": "",
                "creation_time": "",
                "security_status": "",
                "contract_status": "",
                "update_status": ""
            },
            "upload_history": []
        }
    
    def save_config(self) -> bool:
        """
        保存配置到文件
        
        Returns:
            bool: 是否保存成功
        """
        try:
            # 更新最后修改时间
            self.config["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            
            print(f"配置文件保存成功: {self.config_file}")
            return True
        except Exception as e:
            print(f"配置文件保存失败: {e}")
            return False
    
    def get_cookie(self) -> str:
        """
        获取Cookie
        
        Returns:
            str: Cookie字符串
        """
        return self.config.get("cookie", "")
    
    def set_cookie(self, cookie: str) -> bool:
        """
        设置Cookie
        
        Args:
            cookie: Cookie字符串
            
        Returns:
            bool: 是否设置成功
        """
        self.config["cookie"] = cookie
        return self.save_config()
    
    def get_book_id(self) -> str:
        """
        获取书本ID
        
        Returns:
            str: 书本ID
        """
        return self.config.get("book_id", "")
    
    def set_book_id(self, book_id: str) -> bool:
        """
        设置书本ID
        
        Args:
            book_id: 书本ID
            
        Returns:
            bool: 是否设置成功
        """
        self.config["book_id"] = book_id
        return self.save_config()
    
    def get_book_info(self) -> Dict:
        """
        获取书本信息
        
        Returns:
            Dict: 书本信息
        """
        return self.config.get("book_info", {})
    
    def set_book_info(self, book_info: Dict) -> bool:
        """
        设置书本信息
        
        Args:
            book_info: 书本信息字典
            
        Returns:
            bool: 是否设置成功
        """
        self.config["book_info"] = book_info
        return self.save_config()
    
    def add_upload_record(self, chapter_title: str, status: str, message: str = "") -> bool:
        """
        添加上传记录
        
        Args:
            chapter_title: 章节标题
            status: 上传状态
            message: 消息
            
        Returns:
            bool: 是否添加成功
        """
        record = {
            "chapter_title": chapter_title,
            "status": status,
            "message": message,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        if "upload_history" not in self.config:
            self.config["upload_history"] = []
        
        self.config["upload_history"].append(record)
        
        # 只保留最近100条记录
        if len(self.config["upload_history"]) > 100:
            self.config["upload_history"] = self.config["upload_history"][-100:]
        
        return self.save_config()
    
    def get_upload_history(self) -> list:
        """
        获取上传历史记录
        
        Returns:
            list: 上传历史记录列表
        """
        return self.config.get("upload_history", [])
    
    def clear_upload_history(self) -> bool:
        """
        清除上传历史记录
        
        Returns:
            bool: 是否清除成功
        """
        self.config["upload_history"] = []
        return self.save_config()
    
    def export_config(self, export_path: str) -> bool:
        """
        导出配置到指定路径
        
        Args:
            export_path: 导出路径
            
        Returns:
            bool: 是否导出成功
        """
        try:
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print(f"配置导出成功: {export_path}")
            return True
        except Exception as e:
            print(f"配置导出失败: {e}")
            return False
    
    def import_config(self, import_path: str) -> bool:
        """
        从指定路径导入配置
        
        Args:
            import_path: 导入路径
            
        Returns:
            bool: 是否导入成功
        """
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 验证配置格式
            if self._validate_config(imported_config):
                self.config = imported_config
                self.save_config()
                print(f"配置导入成功: {import_path}")
                return True
            else:
                print("配置文件格式不正确")
                return False
        except Exception as e:
            print(f"配置导入失败: {e}")
            return False
    
    def _validate_config(self, config: Dict) -> bool:
        """
        验证配置格式是否正确
        
        Args:
            config: 配置字典
            
        Returns:
            bool: 是否有效
        """
        required_keys = ["cookie", "book_id", "book_info"]
        return all(key in config for key in required_keys)
