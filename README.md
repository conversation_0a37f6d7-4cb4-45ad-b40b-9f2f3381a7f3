# 番茄小说自动上传工具

一个基于 Python 和 PySide6 开发的番茄小说作家工作台自动上传工具，可以批量上传按固定格式编写的小说章节到草稿箱。

## 功能特性

- 📖 支持固定格式的 txt 小说文件解析（#**章节标题**# 格式）
- 🚀 基于 Chrome 浏览器自动化的真实上传功能
- 🖥️ 简洁美观的 GUI 界面
- 📊 实时显示上传进度和章节状态
- 📝 详细的运行日志记录
- ⚙️ Cookie 和书本 ID 验证功能
- 🤖 Selenium WebDriver 驱动的浏览器填表操作

## 安装要求

- Python 3.7+
- Windows 10/11
- Google Chrome 浏览器
- ChromeDriver（与 Chrome 版本匹配）

## 安装依赖

### 1. 安装 Python 依赖库

```bash
pip install -r requirements.txt
```

### 2. 安装 ChromeDriver

有两种方式安装 ChromeDriver：

**方式一：自动安装（推荐）**

```bash
# Selenium 4.6+ 支持自动管理ChromeDriver
# 程序会自动下载匹配的ChromeDriver版本
```

**方式二：手动安装**

1. 查看 Chrome 浏览器版本：chrome://version/
2. 从 [ChromeDriver 官网](https://chromedriver.chromium.org/) 下载匹配版本
3. 将 chromedriver.exe 放到系统 PATH 路径中

### 3. 验证安装

运行程序前可以验证安装：

```bash
python -c "from selenium import webdriver; print('Selenium安装成功')"
```

## 使用方法

### 1. 启动程序

```bash
python main.py
```

### 2. 准备工作

#### 获取 Cookie 信息

1. 打开浏览器，登录番茄小说作家工作台
2. 按 F12 打开开发者工具
3. 在 Network 标签页中刷新页面
4. 找到任意请求，复制完整的 Cookie 字符串

#### 获取书本 ID

1. 在作家工作台中进入要上传的小说管理页面
2. 从 URL 中获取书本 ID（通常是一串数字）

### 3. 准备小说文件

小说 txt 文件必须按以下格式编写：

```
#**第一章 开始**#
这里是第一章的正文内容...

#**第二章 转折**#
这里是第二章的正文内容...

#**第三章 高潮**#
这里是第三章的正文内容...
```

### 4. 使用流程

1. **配置信息**

   - 输入从浏览器获取的 Cookie 信息
   - 输入小说书本 ID
   - 点击"验证 Cookie"和"获取书本信息"确认配置正确

2. **文件处理**

   - 点击"选择文件"选择准备好的 txt 小说文件
   - 点击"解析文件"解析章节内容

3. **章节上传**

   - 检查章节列表中的章节标题和字数
   - 点击"开始上传"批量上传所有章节
   - 实时查看上传进度和状态

4. **查看结果**
   - 在日志区域查看详细的上传过程
   - 上传完成后会显示成功和失败的统计信息

## 文件结构

```
FanqieBookUpload/
├── main.py                    # 程序入口
├── requirements.txt           # 依赖库列表
├── README.md                 # 说明文档
├── 需求.txt                  # 原始需求文档
├── example_novel.txt         # 示例小说文件
└── src/                      # 源码目录
    ├── __init__.py
    ├── core/                 # 核心功能模块
    │   ├── __init__.py
    │   ├── novel_parser.py   # 小说解析器
    │   └── fanqie_uploader.py # 番茄小说上传器
    └── ui/                   # 用户界面模块
        ├── __init__.py
        └── main_window.py    # 主窗口
```

## 注意事项

1. **Cookie 有效期**：Cookie 信息有时效性，如果上传失败请重新获取
2. **上传频率**：程序会自动在章节间添加延迟，避免请求过于频繁
3. **网络连接**：确保网络连接稳定，避免上传中断
4. **文件格式**：严格按照指定格式编写小说文件，标题必须用 #**标题**# 包围
5. **字符编码**：txt 文件必须使用 UTF-8 编码保存

## 故障排除

### 常见问题

1. **导入错误**

   - 确保已安装所有依赖库：`pip install -r requirements.txt`

2. **Cookie 验证失败**

   - 重新登录番茄小说作家工作台
   - 获取最新的 Cookie 信息

3. **文件解析失败**

   - 检查 txt 文件格式是否正确
   - 确保文件使用 UTF-8 编码

4. **上传失败**
   - 检查网络连接
   - 验证书本 ID 是否正确
   - 确认 Cookie 未过期

## 开发说明

本工具采用模块化设计：

- **core 模块**：核心业务逻辑，包括文本解析和网络上传
- **ui 模块**：用户界面，基于 PySide6 实现
- **多线程设计**：上传过程在后台线程进行，不会阻塞界面

## 免责声明

本工具仅供学习和个人使用，请遵守番茄小说平台的使用条款。使用本工具造成的任何后果由使用者自行承担。

## 许可证

MIT License
