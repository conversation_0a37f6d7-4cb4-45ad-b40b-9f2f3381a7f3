"""
主窗口界面 - 标签页版本
"""
import os
import sys
from datetime import datetime
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QTextEdit, QFileDialog,
    QTableWidget, QTableWidgetItem, QProgressBar, QMessageBox,
    QGroupBox, QSplitter, QHeaderView, QCheckBox, QTabWidget,
    QDateTimeEdit, QSpinBox, QComboBox, QPlainTextEdit, QDialog,
    QScrollArea
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer, QDateTime
from PySide6.QtGui import QFont, QIcon
from typing import List, Dict

from src.core.novel_parser import NovelParser
from src.core.fanqie_uploader import FanqieUploader
from src.core.config_manager import ConfigManager
from src.ui.book_info_widget import BookInfoWidget

# 导入许可证相关模块
try:
    # 添加license目录到Python路径
    license_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'license')
    if license_path not in sys.path:
        sys.path.append(license_path)
    
    from machine_code_generator import MachineCodeGenerator
    from src.core.license_manager import LicenseManager
    LICENSE_AVAILABLE = True
except ImportError:
        LICENSE_AVAILABLE = False


class ChapterContentDialog(QDialog):
    """章节内容查看对话框"""
    
    def __init__(self, chapter_data: dict, parent=None):
        super().__init__(parent)
        self.chapter_data = chapter_data
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle(f"章节内容 - {self.chapter_data['title']}")
        self.setGeometry(200, 200, 800, 600)
        
        # 创建主布局
        layout = QVBoxLayout(self)
        
        # 章节信息区域
        info_group = QGroupBox("章节信息")
        info_layout = QGridLayout(info_group)
        
        # 原始标题
        info_layout.addWidget(QLabel("原始标题:"), 0, 0)
        title_label = QLabel(self.chapter_data.get('original_title', self.chapter_data['title']))
        title_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        info_layout.addWidget(title_label, 0, 1)
        
        # 章节数
        info_layout.addWidget(QLabel("章节数:"), 1, 0)
        chapter_number = self.chapter_data.get('chapter_number', 'N/A')
        number_label = QLabel(str(chapter_number) if chapter_number is not None else 'N/A')
        info_layout.addWidget(number_label, 1, 1)
        
        # 章节名称
        info_layout.addWidget(QLabel("章节名称:"), 2, 0)
        chapter_name = self.chapter_data.get('chapter_name', '未知')
        name_label = QLabel(chapter_name)
        info_layout.addWidget(name_label, 2, 1)
        
        # 字数统计
        info_layout.addWidget(QLabel("字数:"), 3, 0)
        word_count_label = QLabel(f"{self.chapter_data['word_count']} 字")
        info_layout.addWidget(word_count_label, 3, 1)
        
        layout.addWidget(info_group)
        
        # 章节内容区域
        content_group = QGroupBox("解析后的章节内容")
        content_layout = QVBoxLayout(content_group)
        
        # 提示信息
        tip_label = QLabel("💡 提示：以下是解析后的章节内容，保持原txt文件的格式，仅去除了空行")
        tip_label.setStyleSheet("""
            QLabel {
                background-color: #e8f4fd;
                color: #31708f;
                border: 1px solid #bee5eb;
                border-radius: 4px;
                padding: 8px;
                margin-bottom: 10px;
            }
        """)
        content_layout.addWidget(tip_label)
        
        # 内容显示区域
        self.content_text = QTextEdit()
        self.content_text.setPlainText(self.chapter_data['content'])
        self.content_text.setReadOnly(True)
        
        # 设置字体
        font = QFont()
        font.setFamily("微软雅黑")
        font.setPointSize(11)
        self.content_text.setFont(font)
        
        # 设置样式
        self.content_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 10px;
                background-color: #ffffff;
                line-height: 1.6;
            }
        """)
        
        content_layout.addWidget(self.content_text)
        layout.addWidget(content_group)
        
        # 底部按钮区域
        button_layout = QHBoxLayout()
        
        # 复制内容按钮
        copy_btn = QPushButton("📋 复制内容")
        copy_btn.clicked.connect(self.copy_content)
        copy_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        button_layout.addWidget(copy_btn)
        
        button_layout.addStretch()
        
        # 关闭按钮
        close_btn = QPushButton("✖️ 关闭")
        close_btn.clicked.connect(self.accept)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
    def copy_content(self):
        """复制章节内容到剪贴板"""
        try:
            from PySide6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(self.chapter_data['content'])
            
            # 显示提示
            QMessageBox.information(self, "复制成功", "章节内容已复制到剪贴板！")
        except Exception as e:
            QMessageBox.critical(self, "复制失败", f"复制失败: {str(e)}")


class UploadThread(QThread):
    """上传线程"""
    
    progress_signal = Signal(str)  # 进度信号
    status_signal = Signal(int, str, str)  # 状态信号 (章节索引, 状态, 章节标题)
    finished_signal = Signal(dict)  # 完成信号
    popup_detected_signal = Signal()  # 弹窗检测信号
    
    def __init__(self, uploader: FanqieUploader, chapters: List[Dict]):
        super().__init__()
        self.uploader = uploader
        self.chapters = chapters
        
    def run(self):
        """运行上传任务"""
        # 修改上传逻辑，检测弹窗
        total_chapters = len(self.chapters)
        success_count = 0
        failed_count = 0
        failed_chapters = []
        
        if self.progress_signal:
            self.progress_signal.emit(f"开始上传 {total_chapters} 个章节...")
        
        for i, chapter in enumerate(self.chapters):
            if self.status_signal:
                self.status_signal.emit(i, 'uploading', chapter['title'])
            
            success, message = self.uploader.upload_chapter(
                chapter['title'], 
                chapter['content'],
                self.progress_signal.emit if self.progress_signal else None
            )
            
            if not success and message == "POPUP_DETECTED":
                # 检测到弹窗，发送信号并停止上传
                self.popup_detected_signal.emit()
                return
            elif success:
                success_count += 1
                if self.status_signal:
                    self.status_signal.emit(i, 'success', chapter['title'])
            else:
                failed_count += 1
                failed_chapters.append({
                    'title': chapter['title'],
                    'error': message
                })
                if self.status_signal:
                    self.status_signal.emit(i, 'failed', chapter['title'])
            
            # 添加延迟避免操作过于频繁
            import time
            time.sleep(3)
        
        result = {
            'total': total_chapters,
            'success': success_count,
            'failed': failed_count,
            'failed_chapters': failed_chapters
        }
        
        if self.progress_signal:
            self.progress_signal.emit(f"上传完成！成功：{success_count}，失败：{failed_count}")
        
        self.finished_signal.emit(result)


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.novel_parser = NovelParser()
        self.config_manager = ConfigManager()
        self.uploader = None
        self.chapters = []
        self.upload_thread = None
        self.has_popup = False  # 弹窗状态
        
        self.init_ui()
        self.load_config()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("番茄小说自动上传工具 v2.0")
        self.setGeometry(100, 100, 1200, 800) 
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标签页容器
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建三个标签页
        self.create_upload_tab()
        self.create_schedule_tab()
        self.create_about_tab()
        
    def create_upload_tab(self):
        """创建书本上传标签页"""
        upload_widget = QWidget()
        
        # 创建主布局
        main_layout = QHBoxLayout(upload_widget)
        
        # 创建左侧区域
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 创建右侧区域（书本信息）
        self.book_info_widget = BookInfoWidget()
        self.book_info_widget.setMinimumWidth(400)
        self.book_info_widget.setMaximumWidth(500)
        
        # 左侧分割器
        left_splitter = QSplitter(Qt.Vertical)
        left_layout.addWidget(left_splitter)
        
        # 创建配置区域
        config_group = self.create_config_group()
        left_splitter.addWidget(config_group)
        
        # 创建文件处理区域
        file_group = self.create_file_group()
        left_splitter.addWidget(file_group)
        
        # 创建章节列表区域
        chapter_group = self.create_chapter_group()
        left_splitter.addWidget(chapter_group)
        
        # 创建日志区域
        log_group = self.create_log_group()
        left_splitter.addWidget(log_group)
        
        # 设置分割器比例
        left_splitter.setStretchFactor(0, 1)
        left_splitter.setStretchFactor(1, 1)
        left_splitter.setStretchFactor(2, 4)
        left_splitter.setStretchFactor(3, 2)
        
        # 添加到主布局
        main_layout.addWidget(left_widget, 2)
        main_layout.addWidget(self.book_info_widget, 1)
        
        # 添加到标签页
        self.tab_widget.addTab(upload_widget, "📚 书本上传")
        
    def create_schedule_tab(self):
        """创建定时发布标签页"""
        schedule_widget = QWidget()
        layout = QVBoxLayout(schedule_widget)
        
        # 标题
        title_label = QLabel("📅 定时发布功能")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin: 10px;")
        layout.addWidget(title_label, alignment=Qt.AlignCenter)
        
        # 功能说明
        info_group = QGroupBox("功能说明")
        info_layout = QVBoxLayout(info_group)
        
        info_text = QPlainTextEdit()
        info_text.setPlainText(
            "定时发布功能正在开发中...\n\n"
            "计划功能包括：\n"
            "• 设置每日定时发布时间\n"
            "• 配置发布章节数量\n"
            "• 智能发布策略\n"
            "• 发布日程管理\n"
            "• 自动暂停和恢复\n\n"
            "敬请期待后续版本更新！"
        )
        info_text.setReadOnly(True)
        info_text.setMaximumHeight(200)
        info_layout.addWidget(info_text)
        
        layout.addWidget(info_group)
        
        # 定时发布配置区域
        config_group = QGroupBox("定时发布配置")
        config_layout = QGridLayout(config_group)
        
        # 发布时间设置
        config_layout.addWidget(QLabel("发布时间:"), 0, 0)
        self.publish_time = QDateTimeEdit()
        self.publish_time.setDisplayFormat("yyyy-MM-dd hh:mm:ss")
        self.publish_time.setDateTime(QDateTime.currentDateTime())
        self.publish_time.setEnabled(False)
        config_layout.addWidget(self.publish_time, 0, 1)
        
        # 发布间隔
        config_layout.addWidget(QLabel("发布间隔(小时):"), 1, 0)
        self.publish_interval = QSpinBox()
        self.publish_interval.setRange(1, 24)
        self.publish_interval.setValue(2)
        self.publish_interval.setEnabled(False)
        config_layout.addWidget(self.publish_interval, 1, 1)
        
        # 每次发布章节数
        config_layout.addWidget(QLabel("每次发布章节数:"), 2, 0)
        self.chapters_per_publish = QSpinBox()
        self.chapters_per_publish.setRange(1, 10)
        self.chapters_per_publish.setValue(1)
        self.chapters_per_publish.setEnabled(False)
        config_layout.addWidget(self.chapters_per_publish, 2, 1)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_schedule_btn = QPushButton("开始定时发布")
        self.start_schedule_btn.setEnabled(False)
        self.start_schedule_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        button_layout.addWidget(self.start_schedule_btn)
        
        self.stop_schedule_btn = QPushButton("停止定时发布")
        self.stop_schedule_btn.setEnabled(False)
        self.stop_schedule_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        button_layout.addWidget(self.stop_schedule_btn)
        
        button_layout.addStretch()
        config_layout.addLayout(button_layout, 3, 0, 1, 2)
        
        layout.addWidget(config_group)
        
        # 发布日志
        log_group = QGroupBox("发布日志")
        log_layout = QVBoxLayout(log_group)
        
        self.schedule_log = QTextEdit()
        self.schedule_log.setReadOnly(True)
        self.schedule_log.setMaximumHeight(150)
        self.schedule_log.setPlainText("定时发布功能暂未启用...")
        log_layout.addWidget(self.schedule_log)
        
        layout.addWidget(log_group)
        
        layout.addStretch()
        
        # 添加到标签页
        self.tab_widget.addTab(schedule_widget, "⏰ 定时发布")
        
    def create_about_tab(self):
        """创建关于我们标签页"""
        about_widget = QWidget()
        layout = QVBoxLayout(about_widget)
        
        # 标题
        title_label = QLabel("ℹ️ 关于我们")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin: 10px;")
        layout.addWidget(title_label, alignment=Qt.AlignCenter)
        
        # 软件信息
        info_group = QGroupBox("软件信息")
        info_layout = QGridLayout(info_group)
        
        info_layout.addWidget(QLabel("软件名称:"), 0, 0)
        info_layout.addWidget(QLabel("番茄小说自动上传工具"), 0, 1)
        
        info_layout.addWidget(QLabel("版本号:"), 1, 0)
        info_layout.addWidget(QLabel("v2.0.0"), 1, 1)
        
        info_layout.addWidget(QLabel("开发者:"), 2, 0)
        info_layout.addWidget(QLabel("尘"), 2, 1)
        
        # 本机机器码
        info_layout.addWidget(QLabel("本机机器码:"), 3, 0)
        machine_code_label = self.get_machine_code_label()
        info_layout.addWidget(machine_code_label, 3, 1)
        
        # 许可证状态
        info_layout.addWidget(QLabel("许可证状态:"), 4, 0)
        license_status_label = self.get_license_status_label()
        info_layout.addWidget(license_status_label, 4, 1)
        
        layout.addWidget(info_group)
        
        # 功能特性
        features_group = QGroupBox("功能特性")
        features_layout = QVBoxLayout(features_group)
        
        features_text = QPlainTextEdit()
        features_text.setPlainText(
            "🔥 核心功能：\n"
            "• 支持批量上传小说章节到番茄小说平台\n"
            "• 智能检测和处理平台弹窗\n"
            "• 实时上传进度监控和状态显示\n"
            "• 支持章节选择性上传\n"
            "• 自动保存配置信息\n\n"
            
            "🎯 用户体验：\n"
            "• 直观的图形界面\n"
            "• 详细的操作日志\n"
            "• 智能错误处理和提示\n"
            "• 支持上传状态实时反馈\n\n"
            
            "🔮 计划功能：\n"
            "• 定时发布章节\n"
            "• 多平台支持\n"
            "• 更多个性化设置"
        )
        features_text.setReadOnly(True)
        features_text.setMaximumHeight(300)
        features_layout.addWidget(features_text)
        
        layout.addWidget(features_group)
        
        # 使用说明
        usage_group = QGroupBox("使用说明")
        usage_layout = QVBoxLayout(usage_group)
        
        usage_text = QPlainTextEdit()
        usage_text.setPlainText(
            "📋 使用步骤：\n"
            "1. 在配置信息中输入有效的Cookie和书本ID\n"
            "2. 点击'获取作品'按钮验证信息并获取书本详情\n"
            "3. 选择要上传的小说txt文件并解析\n"
            "4. 选择需要上传的章节（默认全选）\n"
            "5. 点击'开始上传'开始自动上传\n"
            "6. 如遇到弹窗提示，按照指引手动处理后继续\n\n"
            
            "⚠️ 注意事项：\n"
            "• 请确保Cookie信息的有效性\n"
            "• 上传过程中请不要关闭浏览器\n"
            "• 建议在网络稳定的环境下使用\n"
            "• 遇到问题请查看运行日志"
        )
        usage_text.setReadOnly(True)
        usage_text.setMaximumHeight(200)
        usage_layout.addWidget(usage_text)
        
        layout.addWidget(usage_group)
        
        # 联系方式
        contact_group = QGroupBox("技术支持")
        contact_layout = QVBoxLayout(contact_group)
        
        contact_label = QLabel(
            "如遇到技术问题或需要功能建议，\n"
            "请通过以下方式联系我们：\n\n"
            "📧 邮箱：<EMAIL>\n"
            "💬 微信：JXKJ-2020\n"
            "🌐 官网：https://api.36ma.com"
        )
        contact_label.setStyleSheet("color: #7f8c8d; line-height: 1.5;")
        contact_layout.addWidget(contact_label)
        
        layout.addWidget(contact_group)
        
        layout.addStretch()
        
        # 添加到标签页
        self.tab_widget.addTab(about_widget, "ℹ️ 关于我们")
        
    def create_config_group(self) -> QGroupBox:
        """创建配置区域"""
        group = QGroupBox("配置信息")
        layout = QGridLayout(group)
        
        # Cookie输入
        layout.addWidget(QLabel("Cookie:"), 0, 0)
        self.cookie_edit = QLineEdit()
        self.cookie_edit.setPlaceholderText("请输入从浏览器开发者工具获取的Cookie信息")
        self.cookie_edit.textChanged.connect(self.on_cookie_changed)
        layout.addWidget(self.cookie_edit, 0, 1, 1, 2)  # 跨两列
        
        # 书本ID输入
        layout.addWidget(QLabel("书本ID:"), 1, 0)
        self.book_id_edit = QLineEdit()
        self.book_id_edit.setPlaceholderText("请输入小说书本ID")
        self.book_id_edit.textChanged.connect(self.on_book_id_changed)
        layout.addWidget(self.book_id_edit, 1, 1)
        
        # 合并的获取作品按钮
        self.get_work_btn = QPushButton("获取作品")
        self.get_work_btn.clicked.connect(self.get_work_info)
        self.get_work_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        layout.addWidget(self.get_work_btn, 1, 2)
        
        return group
        
    def create_file_group(self) -> QGroupBox:
        """创建文件处理区域"""
        group = QGroupBox("文件处理")
        layout = QHBoxLayout(group)
        
        # 文件选择
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("请选择小说txt文件")
        self.file_path_edit.setReadOnly(True)
        layout.addWidget(self.file_path_edit)
        
        self.select_file_btn = QPushButton("选择文件")
        self.select_file_btn.clicked.connect(self.select_file)
        layout.addWidget(self.select_file_btn)
        
        self.parse_file_btn = QPushButton("解析文件")
        self.parse_file_btn.clicked.connect(self.parse_file)
        self.parse_file_btn.setEnabled(False)
        layout.addWidget(self.parse_file_btn)
        
        return group
        
    def create_chapter_group(self) -> QGroupBox:
        """创建章节列表区域"""
        group = QGroupBox("章节列表")
        layout = QVBoxLayout(group)
        
        # 创建章节选择控制区域
        selection_layout = QHBoxLayout()
        
        # 全选按钮
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all_chapters)
        self.select_all_btn.setEnabled(False)
        selection_layout.addWidget(self.select_all_btn)
        
        # 反选按钮
        self.invert_selection_btn = QPushButton("反选")
        self.invert_selection_btn.clicked.connect(self.invert_selection)
        self.invert_selection_btn.setEnabled(False)
        selection_layout.addWidget(self.invert_selection_btn)
        
        # 取消选择按钮
        self.deselect_all_btn = QPushButton("取消选择")
        self.deselect_all_btn.clicked.connect(self.deselect_all_chapters)
        self.deselect_all_btn.setEnabled(False)
        selection_layout.addWidget(self.deselect_all_btn)
        
        # 选择状态标签
        self.selection_status_label = QLabel("未选择章节")
        selection_layout.addWidget(self.selection_status_label)
        
        selection_layout.addStretch()
        layout.addLayout(selection_layout)
        
        # 创建表格
        self.chapter_table = QTableWidget()
        self.chapter_table.setColumnCount(7)
        self.chapter_table.setHorizontalHeaderLabels(["选择", "章节标题", "章节数", "章节名称", "字数", "上传状态", "操作"])
        
        # 设置表格属性
        header = self.chapter_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 选择列
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)
        
        # 连接双击事件
        self.chapter_table.cellDoubleClicked.connect(self.on_chapter_double_clicked)
        
        layout.addWidget(self.chapter_table)
        
        # 创建弹窗状态显示区域
        popup_layout = QHBoxLayout()
        popup_layout.addWidget(QLabel("弹窗状态:"))
        self.popup_status_label = QLabel("无弹窗")
        self.popup_status_label.setStyleSheet("""
            QLabel {
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
                border-radius: 4px;
                padding: 4px 8px;
                font-weight: bold;
            }
        """)
        popup_layout.addWidget(self.popup_status_label)
        popup_layout.addStretch()
        layout.addLayout(popup_layout)
        
        # 创建操作按钮区域
        button_layout = QHBoxLayout()
        
        self.upload_all_btn = QPushButton("开始上传")
        self.upload_all_btn.clicked.connect(self.start_upload)
        self.upload_all_btn.setEnabled(False)
        button_layout.addWidget(self.upload_all_btn)
        
        self.continue_upload_btn = QPushButton("继续上传")
        self.continue_upload_btn.clicked.connect(self.continue_upload)
        self.continue_upload_btn.setEnabled(False)
        self.continue_upload_btn.setVisible(False)
        button_layout.addWidget(self.continue_upload_btn)
        
        self.stop_upload_btn = QPushButton("停止上传")
        self.stop_upload_btn.clicked.connect(self.stop_upload)
        self.stop_upload_btn.setEnabled(False)
        button_layout.addWidget(self.stop_upload_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        button_layout.addWidget(self.progress_bar)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        return group
        
    def create_log_group(self) -> QGroupBox:
        """创建日志区域"""
        group = QGroupBox("运行日志")
        layout = QVBoxLayout(group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(150)
        layout.addWidget(self.log_text)
        
        # 日志操作按钮
        log_button_layout = QHBoxLayout()
        
        clear_log_btn = QPushButton("清空日志")
        clear_log_btn.clicked.connect(self.clear_log)
        log_button_layout.addWidget(clear_log_btn)
        
        save_log_btn = QPushButton("保存日志")
        save_log_btn.clicked.connect(self.save_log)
        log_button_layout.addWidget(save_log_btn)
        
        log_button_layout.addStretch()
        layout.addLayout(log_button_layout)
        
        return group
    
    def load_config(self):
        """加载配置"""
        try:
            cookie = self.config_manager.get_cookie()
            book_id = self.config_manager.get_book_id()
            book_info = self.config_manager.get_book_info()
            
            # 填充界面
            if cookie:
                self.cookie_edit.setText(cookie)
                
            if book_id:
                self.book_id_edit.setText(book_id)
                
            if book_info and book_info.get('title'):
                self.book_info_widget.update_book_info(book_info)
                self.log_message(f"已加载书本信息: {book_info['title']}")
                
            self.log_message("配置加载完成")
            
        except Exception as e:
            self.log_message(f"配置加载失败: {str(e)}")
    
    def on_cookie_changed(self):
        """Cookie改变时的处理"""
        cookie = self.cookie_edit.text().strip()
        self.config_manager.set_cookie(cookie)
        
    def on_book_id_changed(self):
        """书本ID改变时的处理"""
        book_id = self.book_id_edit.text().strip()
        self.config_manager.set_book_id(book_id)
        
    def log_message(self, message: str):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        
    def select_file(self):
        """选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择小说txt文件", "", "文本文件 (*.txt)"
        )
        
        if file_path:
            self.file_path_edit.setText(file_path)
            self.parse_file_btn.setEnabled(True)
            self.log_message(f"已选择文件: {os.path.basename(file_path)}")
            
    def parse_file(self):
        """解析文件"""
        file_path = self.file_path_edit.text()
        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "警告", "请先选择有效的文件")
            return
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 验证格式
            is_valid, message = self.novel_parser.validate_format(content)
            if not is_valid:
                QMessageBox.critical(self, "格式错误", message)
                self.log_message(f"文件格式验证失败: {message}")
                return
                
            # 解析章节
            novel_info = self.novel_parser.get_novel_info(content)
            self.chapters = novel_info['chapters']
            
            # 更新章节表格
            self.update_chapter_table()
            
            self.log_message(f"文件解析成功: {novel_info['total_chapters']} 个章节，共 {novel_info['total_words']} 字")
            
            # 启用上传按钮
            self.check_upload_ready()
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"文件解析失败: {str(e)}")
            self.log_message(f"文件解析失败: {str(e)}")
    
    def check_upload_ready(self):
        """检查是否可以开始上传"""
        if self.chapters and self.cookie_edit.text().strip() and self.book_id_edit.text().strip():
            self.upload_all_btn.setEnabled(True)
        else:
            self.upload_all_btn.setEnabled(False)
            
    def update_chapter_table(self):
        """更新章节表格"""
        self.chapter_table.setRowCount(len(self.chapters))
        
        for i, chapter in enumerate(self.chapters):
            # 选择复选框
            checkbox = QCheckBox()
            checkbox.setChecked(True)  # 默认全选
            checkbox.stateChanged.connect(self.update_selection_status)
            self.chapter_table.setCellWidget(i, 0, checkbox)
            
            # 章节标题（原始标题）
            title_item = QTableWidgetItem(chapter['title'])
            self.chapter_table.setItem(i, 1, title_item)
            
            # 章节数
            chapter_number = chapter.get('chapter_number')
            if chapter_number is not None:
                number_item = QTableWidgetItem(str(chapter_number))
            else:
                number_item = QTableWidgetItem("N/A")
            self.chapter_table.setItem(i, 2, number_item)
            
            # 章节名称
            chapter_name = chapter.get('chapter_name', '未知')
            name_item = QTableWidgetItem(chapter_name)
            self.chapter_table.setItem(i, 3, name_item)
            
            # 字数
            word_count_item = QTableWidgetItem(str(chapter['word_count']))
            self.chapter_table.setItem(i, 4, word_count_item)
            
            # 上传状态
            status_item = QTableWidgetItem("待上传")
            self.chapter_table.setItem(i, 5, status_item)
            
            # 操作按钮（暂时留空）
            action_item = QTableWidgetItem("")
            self.chapter_table.setItem(i, 6, action_item)
        
        # 启用选择按钮
        self.select_all_btn.setEnabled(True)
        self.invert_selection_btn.setEnabled(True)
        self.deselect_all_btn.setEnabled(True)
        
        # 更新选择状态
        self.update_selection_status()
            
    def get_work_info(self):
        """获取作品信息（合并验证Cookie和获取书本信息）"""
        book_id = self.book_id_edit.text().strip()
        cookie = self.cookie_edit.text().strip()
        
        if not cookie:
            QMessageBox.warning(self, "警告", "请输入Cookie信息")
            return
            
        if not book_id:
            QMessageBox.warning(self, "警告", "请输入书本ID")
            return
        
        # 禁用按钮防止重复点击
        self.get_work_btn.setEnabled(False)
        self.get_work_btn.setText("获取中...")
        
        try:
            self.log_message("正在验证Cookie并获取作品信息，启动浏览器...")
            
            # 创建临时上传器
            temp_uploader = FanqieUploader(cookie, book_id)
            
            # 第一步：验证Cookie
            self.log_message("第一步：验证Cookie...")
            success, message = temp_uploader.validate_cookie()
            if not success:
                temp_uploader.close()
                QMessageBox.critical(self, "Cookie验证失败", f"Cookie验证失败: {message}")
                self.log_message(f"Cookie验证失败: {message}")
                return
            
            self.log_message("Cookie验证成功!")
            
            # 第二步：获取书本信息
            self.log_message("第二步：获取作品信息...")
            success, book_info = temp_uploader.get_book_info()
            temp_uploader.close()  # 关闭浏览器
            
            if success:
                # 保存书本信息到配置
                self.config_manager.set_book_info(book_info)
                
                # 更新书本信息展示
                self.book_info_widget.update_book_info(book_info)
                
                self.log_message(f"作品信息获取成功: {book_info['title']} ({book_info['update_status']})")
                QMessageBox.information(self, "获取成功", 
                                      f"作品信息获取成功!\n\n"
                                      f"书名：{book_info['title']}\n"
                                      f"状态：{book_info['update_status']}\n"
                                      f"签约：{book_info['contract_status']}")
                
                self.check_upload_ready()
            else:
                QMessageBox.critical(self, "获取失败", f"获取作品信息失败: {book_info.get('error', '未知错误')}")
                self.log_message(f"作品信息获取失败: {book_info.get('error', '未知错误')}")
                
        except Exception as e:
            self.log_message(f"获取作品信息异常: {str(e)}")
            QMessageBox.critical(self, "错误", f"获取作品信息时发生错误: {str(e)}")
            
        finally:
            # 恢复按钮状态
            self.get_work_btn.setEnabled(True)
            self.get_work_btn.setText("获取作品")
        
    def start_upload(self):
        """开始上传"""
        if not self.chapters:
            QMessageBox.warning(self, "警告", "请先解析小说文件")
            return
        
        # 获取选中的章节
        selected_chapters = self.get_selected_chapters()
        if not selected_chapters:
            QMessageBox.warning(self, "警告", "请至少选择一个章节进行上传")
            return
            
        cookie = self.cookie_edit.text().strip()
        book_id = self.book_id_edit.text().strip()
        
        if not cookie or not book_id:
            QMessageBox.warning(self, "警告", "请完善Cookie和书本ID信息")
            return
            
        # 创建上传器并验证Cookie
        self.uploader = FanqieUploader(cookie, book_id)
        
        self.log_message(f"正在初始化浏览器和验证Cookie... 将上传 {len(selected_chapters)} 个选中章节")
        
        # 先验证Cookie，建立浏览器连接
        success, message = self.uploader.validate_cookie()
        if not success:
            self.uploader.close()
            QMessageBox.critical(self, "验证失败", f"Cookie验证失败: {message}")
            self.log_message(f"Cookie验证失败: {message}")
            return
        
        self.log_message("Cookie验证成功，检测弹窗状态...")
        
        # 检测弹窗状态
        try:
            # 尝试上传第一个选中章节来检测弹窗
            first_chapter = selected_chapters[0]
            success, result = self.uploader.upload_chapter(
                first_chapter['title'], 
                first_chapter['content']
            )
            
            if not success and result == "POPUP_DETECTED":
                # 检测到弹窗
                self.update_popup_status(True)
                self.log_message("检测到弹窗，请手动完成五个步骤的弹窗关闭")
                QMessageBox.warning(
                    self, 
                    "检测到弹窗", 
                    "检测到番茄小说的引导弹窗！\n\n"
                    "请按照以下步骤操作：\n"
                    "1. 在浏览器中手动完成五个步骤的引导弹窗\n"
                    "2. 确保所有弹窗都已关闭\n"
                    "3. 完成后点击软件界面的'继续上传'按钮\n\n"
                    "注意：不要关闭浏览器窗口！"
                )
                # 保存选中的章节供继续上传使用
                self.selected_chapters_for_upload = selected_chapters
                return
            elif success:
                # 第一个章节上传成功，没有弹窗
                self.update_popup_status(False)
                self.log_message("未检测到弹窗，开始正常上传...")
                
                # 找到这个章节在原始列表中的索引并更新状态
                original_index = self.chapters.index(first_chapter)
                self.update_chapter_status(original_index, 'success', first_chapter['title'])
                
                # 继续上传剩余选中的章节
                remaining_chapters = selected_chapters[1:]
                if remaining_chapters:
                    self.upload_thread = UploadThread(self.uploader, remaining_chapters)
                    self.upload_thread.progress_signal.connect(self.log_message)
                    self.upload_thread.status_signal.connect(self.update_selected_chapter_status)
                    self.upload_thread.finished_signal.connect(self.upload_finished)
                    
                    # 更新UI状态
                    self.upload_all_btn.setEnabled(False)
                    self.stop_upload_btn.setEnabled(True)
                    self.progress_bar.setVisible(True)
                    self.progress_bar.setMaximum(len(selected_chapters))
                    self.progress_bar.setValue(1)  # 第一个章节已完成
                    
                    self.upload_thread.start()
                else:
                    # 只有一个章节，直接完成
                    result = {
                        'total': 1,
                        'success': 1,
                        'failed': 0,
                        'failed_chapters': []
                    }
                    self.upload_finished(result)
            else:
                # 其他错误
                self.log_message(f"第一个章节上传失败: {result}")
                QMessageBox.critical(self, "上传失败", f"第一个章节上传失败: {result}")
                self.uploader.close()
                
        except Exception as e:
            self.log_message(f"检测弹窗状态时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"检测弹窗状态时出错: {str(e)}")
            if self.uploader:
                self.uploader.close()
        
    def stop_upload(self):
        """停止上传"""
        if self.upload_thread and self.upload_thread.isRunning():
            self.upload_thread.terminate()
            self.upload_thread.wait()
            
        self.upload_all_btn.setEnabled(True)
        self.stop_upload_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        self.log_message("上传已停止")
        
    def select_all_chapters(self):
        """全选章节"""
        for i in range(self.chapter_table.rowCount()):
            checkbox = self.chapter_table.cellWidget(i, 0)
            if checkbox:
                checkbox.setChecked(True)
        self.update_selection_status()
    
    def invert_selection(self):
        """反选章节"""
        for i in range(self.chapter_table.rowCount()):
            checkbox = self.chapter_table.cellWidget(i, 0)
            if checkbox:
                checkbox.setChecked(not checkbox.isChecked())
        self.update_selection_status()
    
    def deselect_all_chapters(self):
        """取消选择所有章节"""
        for i in range(self.chapter_table.rowCount()):
            checkbox = self.chapter_table.cellWidget(i, 0)
            if checkbox:
                checkbox.setChecked(False)
        self.update_selection_status()
    
    def update_selection_status(self):
        """更新选择状态显示"""
        selected_count = 0
        total_count = self.chapter_table.rowCount()
        
        for i in range(total_count):
            checkbox = self.chapter_table.cellWidget(i, 0)
            if checkbox and checkbox.isChecked():
                selected_count += 1
        
        if selected_count == 0:
            self.selection_status_label.setText("未选择章节")
            self.upload_all_btn.setEnabled(False)
        elif selected_count == total_count:
            self.selection_status_label.setText(f"已选择全部 {total_count} 个章节")
            self.check_upload_ready()
        else:
            self.selection_status_label.setText(f"已选择 {selected_count}/{total_count} 个章节")
            self.check_upload_ready()
    
    def get_selected_chapters(self):
        """获取选中的章节列表"""
        selected_chapters = []
        for i in range(self.chapter_table.rowCount()):
            checkbox = self.chapter_table.cellWidget(i, 0)
            if checkbox and checkbox.isChecked():
                if i < len(self.chapters):
                    selected_chapters.append(self.chapters[i])
        return selected_chapters
    
    def on_chapter_double_clicked(self, row: int, column: int):
        """处理章节表格双击事件"""
        try:
            # 检查行数是否有效
            if row < 0 or row >= len(self.chapters):
                return
            
            # 获取双击的章节数据
            chapter_data = self.chapters[row]
            
            # 创建并显示章节内容对话框
            dialog = ChapterContentDialog(chapter_data, self)
            dialog.exec()
            
            self.log_message(f"查看章节内容: {chapter_data['title']}")
            
        except Exception as e:
            self.log_message(f"打开章节内容对话框失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开章节内容对话框失败: {str(e)}")

    def update_chapter_status(self, chapter_index: int, status: str, title: str):
        """更新章节状态"""
        status_map = {
            'uploading': '上传中...',
            'success': '上传成功',
            'failed': '上传失败'
        }
        
        status_text = status_map.get(status, status)
        status_item = QTableWidgetItem(status_text)
        self.chapter_table.setItem(chapter_index, 5, status_item)  # 状态列调整为第5列
        
        # 记录上传结果到配置
        self.config_manager.add_upload_record(title, status)
        
        if status in ['success', 'failed']:
            self.progress_bar.setValue(self.progress_bar.value() + 1)
    
    def update_selected_chapter_status(self, relative_index: int, status: str, title: str):
        """更新选中章节的状态（用于上传线程回调）"""
        # 根据章节标题找到在原始列表中的索引
        for i, chapter in enumerate(self.chapters):
            if chapter['title'] == title:
                self.update_chapter_status(i, status, title)
                break
            
    def update_popup_status(self, has_popup: bool):
        """更新弹窗状态显示"""
        self.has_popup = has_popup
        if has_popup:
            self.popup_status_label.setText("有弹窗")
            self.popup_status_label.setStyleSheet("""
                QLabel {
                    background-color: #f8d7da;
                    color: #721c24;
                    border: 1px solid #f5c6cb;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-weight: bold;
                }
            """)
            # 显示继续上传按钮，隐藏开始上传按钮
            self.upload_all_btn.setVisible(False)
            self.continue_upload_btn.setVisible(True)
            self.continue_upload_btn.setEnabled(True)
        else:
            self.popup_status_label.setText("无弹窗")
            self.popup_status_label.setStyleSheet("""
                QLabel {
                    background-color: #d4edda;
                    color: #155724;
                    border: 1px solid #c3e6cb;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-weight: bold;
                }
            """)
            # 显示开始上传按钮，隐藏继续上传按钮
            self.upload_all_btn.setVisible(True)
            self.continue_upload_btn.setVisible(False)
    
    def continue_upload(self):
        """继续上传（处理完弹窗后）"""
        if not self.uploader:
            QMessageBox.warning(self, "警告", "请先初始化上传器")
            return
        
        # 重置弹窗状态
        self.update_popup_status(False)
        
        self.log_message("继续上传选中的章节...")
        
        # 使用之前保存的选中章节列表
        chapters_to_upload = getattr(self, 'selected_chapters_for_upload', self.get_selected_chapters())
        
        # 创建并启动上传线程
        self.upload_thread = UploadThread(self.uploader, chapters_to_upload)
        self.upload_thread.progress_signal.connect(self.log_message)
        self.upload_thread.status_signal.connect(self.update_selected_chapter_status)
        self.upload_thread.finished_signal.connect(self.upload_finished)
        
        # 更新UI状态
        self.continue_upload_btn.setEnabled(False)
        self.stop_upload_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(len(chapters_to_upload))
        self.progress_bar.setValue(0)
        
        self.upload_thread.start()
    
    def upload_finished(self, result: dict):
        """上传完成"""
        self.upload_all_btn.setEnabled(True)
        self.stop_upload_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        # 重置弹窗状态和按钮显示
        self.update_popup_status(False)
        
        # 显示上传结果
        message = f"上传完成！\n总计：{result['total']} 个章节\n成功：{result['success']} 个\n失败：{result['failed']} 个"
        
        if result['failed'] > 0:
            QMessageBox.warning(self, "上传完成", message)
        else:
            QMessageBox.information(self, "上传成功", message)
            
        self.log_message(f"上传任务完成 - 成功: {result['success']}, 失败: {result['failed']}")
        
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        
    def save_log(self):
        """保存日志"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存日志", "upload_log.txt", "文本文件 (*.txt)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                QMessageBox.information(self, "成功", "日志保存成功")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"日志保存失败: {str(e)}")
    
    def get_machine_code_label(self):
        """获取本机机器码标签"""
        try:
            if LICENSE_AVAILABLE:
                generator = MachineCodeGenerator()
                machine_code = generator.generate_machine_code()
                if machine_code:
                    machine_code_label = QLabel(machine_code)
                    machine_code_label.setStyleSheet("""
                        QLabel {
                            font-family: 'Courier New', monospace;
                            background-color: #f8f9fa;
                            border: 1px solid #e9ecef;
                            border-radius: 4px;
                            padding: 4px 8px;
                        }
                    """)
                    return machine_code_label
                else:
                    return QLabel("获取失败")
            else:
                return QLabel("许可证模块未可用")
        except Exception as e:
            return QLabel(f"获取失败: {str(e)}")
    
    def get_license_status_label(self):
        """获取许可证状态标签"""
        try:
            if not LICENSE_AVAILABLE:
                status_label = QLabel("许可证模块未可用")
                status_label.setStyleSheet("color: #6c757d; font-style: italic;")
                return status_label
            
            # 获取当前机器码
            generator = MachineCodeGenerator()
            machine_code = generator.generate_machine_code()
            if not machine_code:
                status_label = QLabel("❌ 无法获取机器码")
                status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
                return status_label
            
            # 查找当前机器的许可证
            licenses = generator.list_licenses()
            current_license = None
            for license in licenses:
                if license['machine_code'] == machine_code and license['is_active']:
                    current_license = license
                    break
            
            if current_license:
                # 检查是否过期
                try:
                    expire_time = datetime.strptime(current_license['expire_time'], "%Y-%m-%d %H:%M:%S")
                    now = datetime.now()
                    
                    if now > expire_time:
                        status_text = f"❌ 已过期\n激活时间: {current_license['created_time']}\n过期时间: {current_license['expire_time']}"
                        status_label = QLabel(status_text)
                        status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
                    else:
                        days_left = (expire_time - now).days
                        status_text = f"✅ 已激活\n激活时间: {current_license['created_time']}\n过期时间: {current_license['expire_time']}\n剩余天数: {days_left} 天"
                        status_label = QLabel(status_text)
                        status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
                except:
                    status_text = f"✅ 已激活\n激活时间: {current_license['created_time']}\n过期时间: {current_license['expire_time']}"
                    status_label = QLabel(status_text)
                    status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
            else:
                status_text = "❌ 未激活\n本机尚未激活许可证"
                status_label = QLabel(status_text)
                status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
            
            status_label.setStyleSheet(status_label.styleSheet() + """
                QLabel {
                    background-color: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 4px;
                    padding: 8px;
                    line-height: 1.4;
                }
            """)
            
            return status_label
            
        except Exception as e:
            status_label = QLabel(f"❌ 获取状态失败: {str(e)}")
            status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
            return status_label
