"""
番茄小说上传器 - 基于Chrome浏览器自动化
"""
import time
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from typing import Dict, List, Callable, Optional, Tuple

from .webdriver_manager import WebDriverManager

class FanqieUploader:
    """番茄小说上传器 - 使用Chrome浏览器自动化操作"""
    
    def __init__(self, cookie_string: str, book_id: str):
        """
        初始化上传器
        
        Args:
            cookie_string: 用户cookie信息字符串
            book_id: 小说书本ID
        """
        self.cookie_string = cookie_string
        self.book_id = book_id
        self.base_url = "https://fanqienovel.com"
        self.driver = None
        self.wait = None
        self.is_logged_in = False
    
    def _setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            # 使用我们的WebDriverManager获取ChromeDriver路径
            webdriver_manager = WebDriverManager()
            chromedriver_path = webdriver_manager.get_chromedriver_path()
            
            if not chromedriver_path:
                return False, "无法获取ChromeDriver，请检查Chrome浏览器是否已安装"
            
            chrome_options = Options()
            # 设置浏览器选项
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--disable-features=VizDisplayCompositor')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # 创建ChromeDriver服务
            service = Service(executable_path=chromedriver_path)
            
            # 创建WebDriver实例
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            
            return True, "浏览器启动成功"
        except Exception as e:
            return False, f"浏览器启动失败: {str(e)}"
    
    def _set_cookies(self):
        """设置Cookie"""
        try:
            # 首先访问网站主页
            print("访问网站主页...")
            self.driver.get(self.base_url)
            time.sleep(3)
            
            # 解析Cookie字符串并设置Cookie
            cookies = self.cookie_string.split(';')
            success_count = 0
            
            for cookie in cookies:
                cookie = cookie.strip()
                if '=' in cookie and cookie:
                    try:
                        name, value = cookie.split('=', 1)
                        name = name.strip()
                        value = value.strip()
                        
                        if name and value:  # 确保name和value都不为空
                            # 尝试不同的域名设置
                            cookie_data = {
                                'name': name,
                                'value': value,
                                'domain': '.fanqienovel.com',
                                'path': '/'
                            }
                            
                            self.driver.add_cookie(cookie_data)
                            success_count += 1
                            print(f"设置Cookie成功: {name}")
                            
                    except Exception as e:
                        print(f"设置Cookie失败 {cookie[:50]}...: {str(e)}")
                        # 尝试使用当前域名
                        try:
                            cookie_data = {
                                'name': name,
                                'value': value,
                                'path': '/'
                            }
                            self.driver.add_cookie(cookie_data)
                            success_count += 1
                            print(f"使用当前域名设置Cookie成功: {name}")
                        except:
                            continue
            
            print(f"成功设置 {success_count} 个Cookie")
            
            if success_count == 0:
                return False, "没有成功设置任何Cookie，请检查Cookie格式"
            
            # 刷新页面使Cookie生效
            print("刷新页面使Cookie生效...")
            self.driver.refresh()
            time.sleep(3)
            
            return True, f"Cookie设置成功，共设置 {success_count} 个Cookie"
            
        except Exception as e:
            return False, f"Cookie设置失败: {str(e)}"
    
    def validate_cookie(self) -> Tuple[bool, str]:
        """
        验证cookie是否有效
        
        Returns:
            Tuple[bool, str]: (是否有效, 消息)
        """
        try:
            # 设置浏览器
            success, message = self._setup_driver()
            if not success:
                return False, message
            
            # 设置Cookie
            success, message = self._set_cookies()
            if not success:
                self._cleanup_driver()
                return False, message
            
            # 访问作家工作台验证登录状态
            print("正在验证登录状态...")
            self.driver.get(f"{self.base_url}/main/writer/home")
            time.sleep(5)  # 增加等待时间
            
            # 获取页面标题和内容进行更精确的判断
            page_title = self.driver.title
            page_source = self.driver.page_source
            current_url = self.driver.current_url
            
            print(f"当前页面标题: {page_title}")
            print(f"当前页面URL: {current_url}")
            
            # 检查页面内容，输出一些关键信息用于调试
            print("页面内容检查:")
            if "作家" in page_source:
                print("  - 检测到'作家'关键词")
            if "创作" in page_source:
                print("  - 检测到'创作'关键词")
            if "我的" in page_source:
                print("  - 检测到'我的'关键词")
            if "writer" in current_url:
                print("  - URL包含'writer'")
            
            # 明确的登录失败指示器（更严格的判断）
            definite_login_required = [
                "/login" in current_url and "redirect" in current_url,  # 被重定向到登录页
                "passport.bytedance.com" in current_url,  # 跳转到字节跳动登录
                "登录" in page_source and "账号" in page_source,  # 明确的登录提示
            ]
            
            # 明确的登录成功指示器（放宽条件）
            definite_login_success = [
                "writer" in current_url and "/login" not in current_url,  # 在作家相关页面且不是登录页
                "工作台" in page_source and ("作品管理" in page_source or "数据中心" in page_source),  # 包含作家相关内容
                "fanqienovel.com/main/writer/home" in current_url,  # 在作家工作台URL
            ]
            
            # 先检查明确的登录失败情况
            if any(definite_login_required):
                print("检测到明确的登录失败指示器")
                print(f"失败原因: URL包含登录重定向或登录提示")
                self._cleanup_driver()
                return False, "Cookie已过期，已被重定向到登录页面"
            
            # 再检查明确的登录成功情况
            if any(definite_login_success):
                print("检测到明确的登录成功指示器")
                self.is_logged_in = True
                return True, "Cookie验证成功，已登录作家工作台"
            
            # 如果都没有明确检测到，采用更宽松的策略
            print("未检测到明确指示器，采用宽松验证策略...")
            
            # 宽松的成功条件：只要不是明确的登录页面，就认为可能成功
            loose_success_conditions = [
                "fanqienovel.com" in current_url and "/login" not in current_url,  # 在番茄小说域名下且不是登录页
                len(page_source) > 10000,  # 页面内容较多，可能是正常页面
                "writer" in current_url,  # URL包含writer
            ]
            
            if any(loose_success_conditions):
                print("符合宽松成功条件，认为登录成功")
                print("如果后续操作失败，可能需要重新获取Cookie")
                self.is_logged_in = True
                return True, "Cookie验证通过（宽松模式），如有问题请重新获取Cookie"
            
            # 最后尝试：直接认为成功，让用户在后续操作中验证
            print("无法确定登录状态，默认认为成功，请在后续操作中验证")
            self.is_logged_in = True
            return True, "Cookie已设置，请在后续操作中验证是否有效"
            
        except Exception as e:
            self._cleanup_driver()
            return False, f"Cookie验证失败: {str(e)}"
    
    def get_book_info(self) -> Tuple[bool, dict]:
        """
        获取书本信息
        
        Returns:
            Tuple[bool, dict]: (是否成功, 书本信息)
        """
        try:
            if not self.is_logged_in:
                return False, {'error': '请先验证Cookie'}
            
            # 访问书本管理页面
            book_url = f"{self.base_url}/main/writer/book-info/{self.book_id}?isNewSign=1&experienceLevel=undefined&experienceInfo=undefined&type=1"
            self.driver.get(book_url)
            time.sleep(5)  # 增加等待时间确保页面完全加载
            
            print("正在提取书本信息...")
            
            # 初始化书本信息字典
            book_info = {
                'book_id': self.book_id,
                'title': '未知小说',
                'cover_url': '',
                'target_audience': '',
                'tags': '',
                'main_character': '',
                'description': '',
                'creation_time': '',
                'security_status': '',
                'contract_status': '',
                'update_status': '连载中'
            }
            
            try:
                # 等待页面内容加载
                self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".book-detail")))
                
                # 提取书本名称
                try:
                    name_elements = self.driver.find_elements(By.XPATH, "//span[text()='书本名称']/following-sibling::div//span[@class='info-text font-1']")
                    if name_elements:
                        book_info['title'] = name_elements[0].text.strip()
                        print(f"书本名称: {book_info['title']}")
                except Exception as e:
                    print(f"提取书本名称失败: {e}")
                
                # 提取封面URL
                try:
                    cover_elements = self.driver.find_elements(By.CSS_SELECTOR, ".book-cover-img")
                    if cover_elements:
                        style = cover_elements[0].get_attribute("style")
                        if "background-image: url(" in style:
                            start = style.find('url("') + 5
                            end = style.find('")', start)
                            book_info['cover_url'] = style[start:end]
                            print(f"封面URL: {book_info['cover_url'][:50]}...")
                except Exception as e:
                    print(f"提取封面URL失败: {e}")
                
                # 提取目标读者
                try:
                    audience_elements = self.driver.find_elements(By.XPATH, "//span[text()='目标读者']/following-sibling::div//span[@class='info-text font-1']")
                    if audience_elements:
                        book_info['target_audience'] = audience_elements[0].text.strip()
                        print(f"目标读者: {book_info['target_audience']}")
                except Exception as e:
                    print(f"提取目标读者失败: {e}")
                
                # 提取标签 - 简化定位策略
                try:
                    # 直接通过book-info-item包含"标签"文本查找
                    tags_elements = self.driver.find_elements(By.XPATH, "//div[contains(@class,'book-info-item')][.//text()[contains(.,'标签')]]//div[last()]//span[@class='info-text font-1']")
                    if tags_elements:
                        book_info['tags'] = tags_elements[0].text.strip()
                        print(f"标签: {book_info['tags']}")
                    else:
                        # 备用：更精确的查找
                        all_items = self.driver.find_elements(By.XPATH, "//div[@class='book-info-item']")
                        for item in all_items:
                            if '标签' in item.text:
                                text_span = item.find_element(By.XPATH, ".//span[@class='info-text font-1']")
                                book_info['tags'] = text_span.text.strip()
                                print(f"标签(备用): {book_info['tags']}")
                                break
                except Exception as e:
                    print(f"提取标签失败: {e}")
                
                # 提取主角名 - 简化定位策略  
                try:
                    # 直接通过book-info-item包含"主角名"文本查找
                    character_elements = self.driver.find_elements(By.XPATH, "//div[contains(@class,'book-info-item')][.//text()[contains(.,'主角名')]]//div[last()]//span[@class='info-text font-1']")
                    if character_elements:
                        book_info['main_character'] = character_elements[0].text.strip()
                        print(f"主角名: {book_info['main_character']}")
                    else:
                        # 备用：更精确的查找
                        all_items = self.driver.find_elements(By.XPATH, "//div[@class='book-info-item']")
                        for item in all_items:
                            if '主角名' in item.text:
                                text_span = item.find_element(By.XPATH, ".//span[@class='info-text font-1']")
                                book_info['main_character'] = text_span.text.strip()
                                print(f"主角名(备用): {book_info['main_character']}")
                                break
                except Exception as e:
                    print(f"提取主角名失败: {e}")
                
                # 提取作品简介 - 根据新的HTML结构
                try:
                    # 方法1：通过info-label查找作品简介
                    desc_elements = self.driver.find_elements(By.XPATH, "//span[@class='info-label font-2' and text()='作品简介']/../following-sibling::div//span[@class='info-text font-1']")
                    if desc_elements:
                        book_info['description'] = desc_elements[0].text.strip()
                        print(f"作品简介: {book_info['description'][:50]}...")
                    else:
                        # 方法2：备用查找方式
                        desc_elements = self.driver.find_elements(By.XPATH, "//span[text()='作品简介']/following-sibling::div//span[@class='info-text font-1']")
                        if desc_elements:
                            book_info['description'] = desc_elements[0].text.strip()
                            print(f"作品简介(备用): {book_info['description'][:50]}...")
                except Exception as e:
                    print(f"提取作品简介失败: {e}")
                
                # 提取书号（确认与传入的book_id一致）
                try:
                    id_elements = self.driver.find_elements(By.XPATH, "//span[text()='书号']/following-sibling::div//span[@class='info-text font-1']")
                    if id_elements:
                        extracted_id = id_elements[0].text.strip()
                        if extracted_id != self.book_id:
                            print(f"警告: 提取的书号({extracted_id})与传入的book_id({self.book_id})不一致")
                        print(f"书号: {extracted_id}")
                except Exception as e:
                    print(f"提取书号失败: {e}")
                
                # 提取创建时间
                try:
                    time_elements = self.driver.find_elements(By.XPATH, "//span[text()='创建时间']/following-sibling::div//span[@class='info-text font-1']")
                    if time_elements:
                        book_info['creation_time'] = time_elements[0].text.strip()
                        print(f"创建时间: {book_info['creation_time']}")
                except Exception as e:
                    print(f"提取创建时间失败: {e}")
                
                # 提取安全状态
                try:
                    security_elements = self.driver.find_elements(By.XPATH, "//span[text()='安全状态']/following-sibling::div//span[@class='info-text font-1']")
                    if security_elements:
                        book_info['security_status'] = security_elements[0].text.strip()
                        print(f"安全状态: {book_info['security_status']}")
                except Exception as e:
                    print(f"提取安全状态失败: {e}")
                
                # 提取签约状态
                try:
                    contract_elements = self.driver.find_elements(By.XPATH, "//span[text()='签约状态']/following-sibling::div//span[@class='info-text font-1']")
                    if contract_elements:
                        book_info['contract_status'] = contract_elements[0].text.strip()
                        print(f"签约状态: {book_info['contract_status']}")
                except Exception as e:
                    print(f"提取签约状态失败: {e}")
                
                # 提取更新状态
                try:
                    update_elements = self.driver.find_elements(By.XPATH, "//span[text()='更新状态']/following-sibling::div//span[@class='info-text font-1']")
                    if update_elements:
                        book_info['update_status'] = update_elements[0].text.strip()
                        print(f"更新状态: {book_info['update_status']}")
                except Exception as e:
                    print(f"提取更新状态失败: {e}")
                
                print("书本信息提取完成！")
                return True, book_info
                
            except TimeoutException:
                print("页面加载超时，使用基础信息")
                return True, book_info
                
        except Exception as e:
            print(f"获取书本信息异常: {str(e)}")
            return False, {'error': str(e)}
    
    def check_popup_exists(self) -> bool:
        """
        检测是否存在弹窗
        
        Returns:
            bool: 是否存在弹窗
        """
        try:
            if not self.driver:
                return False
            
            print("检查是否存在引导弹窗...")
            
            # 检查引导弹窗的各种可能指示器
            popup_indicators = [
                ".publish-tour-guide",
                ".reactour__helper--is-open", 
                "div[class*='sc-dcJsrY'][class*='reactour__helper']",
                ".guide-card-footer-btn",
                "div[class*='reactour__helper']",
                ".arco-modal:not([style*='display: none'])"
            ]
            
            for indicator in popup_indicators:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, indicator)
                    for element in elements:
                        if element.is_displayed():
                            print(f"检测到弹窗指示器: {indicator}")
                            return True
                except Exception as e:
                    continue
            
            # 检查是否有包含特定文本的按钮（引导弹窗按钮）
            try:
                button_texts = ["下一步", "我知道了", "开始", "继续"]
                for text in button_texts:
                    buttons = self.driver.find_elements(By.XPATH, f"//button[contains(text(), '{text}')]")
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            # 进一步检查按钮是否在弹窗容器中
                            parent_element = button.find_element(By.XPATH, "./ancestor::div[contains(@class, 'guide') or contains(@class, 'tour') or contains(@class, 'reactour') or contains(@class, 'modal')]")
                            if parent_element and parent_element.is_displayed():
                                print(f"检测到弹窗按钮: {text}")
                                return True
            except Exception as e:
                pass
            
            print("未检测到弹窗")
            return False
            
        except Exception as e:
            print(f"检测弹窗时出现异常: {e}")
            return False
    
    def upload_chapter(self, chapter_title: str, chapter_content: str, 
                      progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """
        上传单个章节到草稿箱
        
        Args:
            chapter_title: 章节标题
            chapter_content: 章节内容
            progress_callback: 进度回调函数
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            if progress_callback:
                progress_callback(f"正在上传章节：{chapter_title}")
            
            # 检查浏览器是否还在运行
            if not self.driver:
                return False, "浏览器已关闭，请重新验证Cookie"
            
            print(f"开始上传章节: {chapter_title}")
            
            # 访问章节创建页面
            create_url = f"{self.base_url}/main/writer/{self.book_id}/publish/?enter_from=newchapter"
            print(f"访问创建页面: {create_url}")
            
            try:
                self.driver.get(create_url)
                time.sleep(10)  # 增加等待时间
                
                # 检查是否被重定向到登录页
                current_url = self.driver.current_url
                if "/login" in current_url or "passport" in current_url:
                    return False, "Cookie已过期，已被重定向到登录页面"
                
                print(f"当前页面URL: {current_url}")

                # 检测弹窗状态
                time.sleep(15)  # 等待页面完全加载
                
                # 检查是否存在弹窗
                has_popup = self.check_popup_exists()
                
                if has_popup:
                    print("检测到弹窗存在，需要手动处理")
                    return False, "POPUP_DETECTED"  # 返回特殊标识，表示检测到弹窗
                    
                print("未检测到弹窗，继续正常上传流程")

            except Exception as e:
                return False, f"访问创建页面失败: {str(e)}"
            
            # 提取章节数字和章节名称
            from .novel_parser import NovelParser
            parser = NovelParser()
            chapter_number, chapter_name = parser.extract_chapter_info(chapter_title)
            
            print(f"解析章节信息: 章节数={chapter_number}, 章节名称={chapter_name}")
            
            # 1. 填写章节数（阿拉伯数字）
            try:
                print("正在查找章节数输入框...")
                # 根据HTML结构，章节数输入框在 .left-input 内
                chapter_number_input = self.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".left-input input.serial-input"))
                )
                print("找到章节数输入框")
                
                chapter_number_input.clear()
                if chapter_number is not None:
                    chapter_number_input.send_keys(str(chapter_number))
                    print(f"章节数输入完成: {chapter_number}")
                else:
                    # 如果没有解析到章节数，尝试从标题中提取数字
                    import re
                    numbers = re.findall(r'\d+', chapter_title)
                    if numbers:
                        chapter_number_input.send_keys(numbers[0])
                        print(f"使用提取的数字: {numbers[0]}")
                    else:
                        chapter_number_input.send_keys("1")
                        print("使用默认章节数: 1")
                
                time.sleep(1)
                
            except Exception as e:
                return False, f"填写章节数失败: {str(e)}"
            
            # 2. 填写章节名称
            try:
                print("正在查找章节名称输入框...")
                # 根据HTML结构，章节名称输入框有placeholder="请输入标题"
                chapter_name_input = self.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "input[placeholder='请输入标题']"))
                )
                print("找到章节名称输入框")
                
                chapter_name_input.clear()
                chapter_name_input.send_keys(chapter_name)
                print(f"章节名称输入完成: {chapter_name}")
                time.sleep(1)
                
            except Exception as e:
                return False, f"填写章节名称失败: {str(e)}"
            
            # 3. 填写章节内容
            try:
                print("正在查找内容编辑框...")
                # 根据HTML结构，内容编辑器是 .ProseMirror 类的 contenteditable div
                content_element = self.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".ProseMirror[contenteditable='true']"))
                )
                print("找到ProseMirror内容编辑框")
                
                # 点击编辑框激活
                content_element.click()
                time.sleep(1)
                
                # 清空内容（使用Ctrl+A + Delete）
                from selenium.webdriver.common.keys import Keys
                content_element.send_keys(Keys.CONTROL + "a")
                content_element.send_keys(Keys.DELETE)
                time.sleep(0.5)
                
                # 输入章节内容
                content_element.send_keys(chapter_content)
                print(f"内容输入完成，字数: {len(chapter_content)}")
                time.sleep(2)
                
            except Exception as e:
                # 如果ProseMirror编辑器失败，尝试其他编辑器
                try:
                    print("ProseMirror编辑器失败，尝试其他编辑器...")
                    content_selectors = [
                        "div[contenteditable='true']",
                        ".syl-editor div[contenteditable='true']",
                        "textarea[placeholder*='内容']",
                        "textarea[name*='content']",
                        ".content-editor textarea",
                        ".ql-editor",
                        "textarea"
                    ]
                    
                    content_element = None
                    for selector in content_selectors:
                        try:
                            content_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                            print(f"找到内容编辑框: {selector}")
                            break
                        except NoSuchElementException:
                            continue
                    
                    if content_element is None:
                        return False, "未找到内容编辑框，请检查页面结构"
                    
                    # 根据元素类型处理输入
                    if content_element.tag_name == "textarea":
                        content_element.clear()
                        content_element.send_keys(chapter_content)
                    else:
                        # contenteditable div
                        content_element.click()
                        time.sleep(0.5)
                        content_element.send_keys(Keys.CONTROL + "a")
                        content_element.send_keys(Keys.DELETE)
                        content_element.send_keys(chapter_content)
                    
                    print(f"内容输入完成，字数: {len(chapter_content)}")
                    time.sleep(2)
                    
                except Exception as inner_e:
                    return False, f"填写内容失败: {str(e)} | 备用方法也失败: {str(inner_e)}"
            
            # 保存到草稿箱
            try:
                print("正在查找保存按钮...")
                # 寻找保存草稿按钮
                save_button = None
                
                # 使用XPath查找包含文本的按钮
                xpath_selectors = [
                    "//button[contains(text(), '保存草稿')]",
                    "//button[contains(text(), '存草稿')]",
                    "//button[contains(text(), '草稿')]",
                    "//button[contains(text(), '保存')]",
                    "//span[contains(text(), '保存草稿')]/parent::button",
                    "//span[contains(text(), '存草稿')]/parent::button"
                ]
                
                for xpath in xpath_selectors:
                    try:
                        save_button = self.driver.find_element(By.XPATH, xpath)
                        print(f"找到保存按钮: {xpath}")
                        break
                    except NoSuchElementException:
                        continue
                
                # 如果XPath没找到，尝试CSS选择器
                if save_button is None:
                    css_selectors = [
                        "button[data-action*='draft']",
                        ".save-draft",
                        "button.draft-btn",
                        "button[type='submit']"
                    ]
                    
                    for selector in css_selectors:
                        try:
                            save_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                            print(f"找到保存按钮: {selector}")
                            break
                        except NoSuchElementException:
                            continue
                
                if save_button is None:
                    # 输出页面上所有按钮用于调试
                    buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    button_texts = [btn.text for btn in buttons if btn.text.strip()]
                    print(f"页面上的所有按钮: {button_texts}")
                    return False, f"未找到保存草稿按钮，页面按钮: {', '.join(button_texts)}"
                
                save_button.click()
                print("点击保存按钮完成")
                time.sleep(3)
                
                if progress_callback:
                    progress_callback(f"章节 '{chapter_title}' 上传成功")
                
                return True, f"章节 '{chapter_title}' 上传成功"
                
            except Exception as e:
                return False, f"保存草稿失败: {str(e)}"
            
        except Exception as e:
            error_msg = f"上传章节 '{chapter_title}' 失败：{str(e)}"
            print(f"上传异常: {error_msg}")
            if progress_callback:
                progress_callback(error_msg)
            return False, error_msg
    
    def upload_chapters(self, chapters: List[Dict], 
                       progress_callback: Optional[Callable] = None,
                       status_callback: Optional[Callable] = None) -> Dict:
        """
        批量上传章节
        
        Args:
            chapters: 章节列表
            progress_callback: 进度回调函数
            status_callback: 状态回调函数
            
        Returns:
            Dict: 上传结果统计
        """
        total_chapters = len(chapters)
        success_count = 0
        failed_count = 0
        failed_chapters = []
        
        if progress_callback:
            progress_callback(f"开始上传 {total_chapters} 个章节...")
        
        for i, chapter in enumerate(chapters):
            if status_callback:
                status_callback(i, 'uploading', chapter['title'])
            
            success, message = self.upload_chapter(
                chapter['title'], 
                chapter['content'],
                progress_callback
            )
            
            if success:
                success_count += 1
                if status_callback:
                    status_callback(i, 'success', chapter['title'])
            else:
                failed_count += 1
                failed_chapters.append({
                    'title': chapter['title'],
                    'error': message
                })
                if status_callback:
                    status_callback(i, 'failed', chapter['title'])
            
            # 添加延迟避免操作过于频繁
            time.sleep(3)
        
        result = {
            'total': total_chapters,
            'success': success_count,
            'failed': failed_count,
            'failed_chapters': failed_chapters
        }
        
        if progress_callback:
            progress_callback(f"上传完成！成功：{success_count}，失败：{failed_count}")
        
        return result
    
    def _cleanup_driver(self):
        """清理浏览器资源"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None
            self.wait = None
            self.is_logged_in = False
    
    def close(self):
        """关闭上传器"""
        self._cleanup_driver()
