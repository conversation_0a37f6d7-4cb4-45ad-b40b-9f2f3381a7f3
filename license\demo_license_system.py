#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
机器码验证系统演示脚本
展示完整的许可证创建、激活和验证流程
"""

import os
import sys
from machine_code_generator import MachineCodeGenerator
from src.core.license_manager import LicenseManager

def demo_license_system():
    """演示许可证系统的完整流程"""
    print("=" * 60)
    print("🎫 番茄小说上传工具 - 机器码验证系统演示")
    print("=" * 60)
    
    # 第一步：创建许可证（管理员端）
    print("\n📋 第一步：管理员创建许可证")
    print("-" * 40)
    
    generator = MachineCodeGenerator()
    
    # 获取机器信息和机器码
    machine_info = generator.get_machine_info()
    machine_code = generator.generate_machine_code(machine_info)
    
    print(f"🖥️  当前机器码: {machine_code}")
    print(f"💻 平台信息: {machine_info.get('platform', 'Unknown')}")
    print(f"🔧 处理器: {machine_info.get('processor', 'Unknown')}")
    
    # 创建许可证
    print("\n正在创建许可证...")
    result, message = generator.create_license(
        user_name="演示用户",
        email="<EMAIL>",
        expire_days=365,
        max_devices=1,
        notes="系统演示许可证"
    )
    
    if result:
        print(f"✅ {message}")
        print(f"📄 许可证密钥: {result['license_key']}")
        print(f"⏰ 过期时间: {result['expire_time']}")
        license_key = result['license_key']
    else:
        print(f"❌ 创建失败: {message}")
        return False
    
    # 第二步：用户激活许可证（客户端）
    print("\n📱 第二步：用户激活许可证")
    print("-" * 40)
    
    license_manager = LicenseManager()
    
    # 检查激活前状态
    is_valid, status_message = license_manager.is_licensed()
    print(f"🔍 激活前状态: {status_message}")
    
    # 激活许可证
    print(f"\n正在激活许可证: {license_key}")
    success, activate_message = license_manager.activate_license(license_key)
    
    if success:
        print(f"✅ {activate_message}")
    else:
        print(f"❌ 激活失败: {activate_message}")
        return False
    
    # 第三步：验证许可证状态
    print("\n🔐 第三步：验证许可证状态")
    print("-" * 40)
    
    # 再次检查许可证状态
    is_valid, status_message = license_manager.is_licensed()
    print(f"🎯 验证结果: {status_message}")
    
    if is_valid:
        print("🎉 许可证验证成功！软件可以正常使用。")
    else:
        print("⚠️  许可证验证失败！")
        return False
    
    # 第四步：显示数据库信息
    print("\n📊 第四步：查看数据库信息")
    print("-" * 40)
    
    licenses = generator.list_licenses()
    print(f"📈 数据库中共有 {len(licenses)} 个许可证")
    
    for i, license in enumerate(licenses[-3:], 1):  # 显示最后3个
        status = "有效" if license['is_active'] else "停用"
        print(f"  {i}. 用户: {license['user_name']} | 状态: {status} | 创建: {license['created_time']}")
    
    # 第五步：文件检查
    print("\n📁 第五步：检查生成的文件")
    print("-" * 40)
    
    files_to_check = [
        "license_db.sqlite",
        "license.json",
        f"license_{machine_code}.txt"
    ]
    
    for file_name in files_to_check:
        if os.path.exists(file_name):
            size = os.path.getsize(file_name)
            print(f"✅ {file_name} - {size} 字节")
        else:
            print(f"❌ {file_name} - 不存在")
    
    print("\n" + "=" * 60)
    print("🎊 演示完成！机器码验证系统运行正常。")
    print("=" * 60)
    
    return True


def cleanup_demo_files():
    """清理演示文件（可选）"""
    print("\n🧹 是否清理演示文件？")
    choice = input("输入 'y' 清理，其他键保留: ").strip().lower()
    
    if choice == 'y':
        files_to_clean = [
            "license.json",
        ]
        
        for file_name in files_to_clean:
            try:
                if os.path.exists(file_name):
                    os.remove(file_name)
                    print(f"🗑️  已删除: {file_name}")
            except Exception as e:
                print(f"❌ 删除失败 {file_name}: {e}")
        
        print("✨ 清理完成")
    else:
        print("📁 文件已保留")


if __name__ == "__main__":
    try:
        success = demo_license_system()
        
        if success:
            cleanup_demo_files()
            
    except KeyboardInterrupt:
        print("\n\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n\n❌ 演示过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
