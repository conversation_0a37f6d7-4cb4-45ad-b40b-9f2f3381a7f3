#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化的Chrome版本检测测试
直接导入WebDriverManager类，避免PyArmor问题
"""

import os
import sys
import time
import zipfile
import requests
import platform
from pathlib import Path
from typing import Optional

class SimpleWebDriverManager:
    """简化的WebDriver管理器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.drivers_dir = self.base_dir / "drivers"
        self.drivers_dir.mkdir(exist_ok=True)
        self.system = platform.system().lower()
        
    def get_chrome_version(self) -> Optional[str]:
        """获取Chrome浏览器版本"""
        try:
            if self.system == "windows":
                import winreg
                import subprocess
                
                # 方法1: 从注册表获取Chrome版本 (HKEY_CURRENT_USER)
                try:
                    key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
                    version = winreg.QueryValueEx(key, "version")[0]
                    winreg.CloseKey(key)
                    if version:
                        print(f"从注册表(HKCU)获取Chrome版本: {version}")
                        return version
                except:
                    pass
                
                # 方法2: 从注册表获取Chrome版本 (HKEY_LOCAL_MACHINE)
                try:
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Google\Chrome\BLBeacon")
                    version = winreg.QueryValueEx(key, "version")[0]
                    winreg.CloseKey(key)
                    if version:
                        print(f"从注册表(HKLM)获取Chrome版本: {version}")
                        return version
                except:
                    pass
                
                # 方法3: 使用Chrome命令行获取版本
                chrome_paths = [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
                ]
                
                for chrome_path in chrome_paths:
                    if os.path.exists(chrome_path):
                        try:
                            # 设置subprocess参数，隐藏命令行窗口
                            startupinfo = None
                            creationflags = 0
                            if platform.system() == "Windows":
                                if hasattr(subprocess, 'STARTUPINFO'):
                                    startupinfo = subprocess.STARTUPINFO()
                                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                                    startupinfo.wShowWindow = subprocess.SW_HIDE
                                if hasattr(subprocess, 'CREATE_NO_WINDOW'):
                                    creationflags = subprocess.CREATE_NO_WINDOW
                            
                            # 使用chrome --version命令
                            result = subprocess.run([
                                chrome_path, '--version'
                            ], capture_output=True, text=True, timeout=10,
                               startupinfo=startupinfo, creationflags=creationflags)
                            
                            if result.returncode == 0 and result.stdout:
                                # 解析输出，格式通常是 "Google Chrome 139.0.6778.86"
                                output = result.stdout.strip()
                                print(f"Chrome命令行输出: {output}")
                                if 'Chrome' in output:
                                    parts = output.split()
                                    for part in parts:
                                        if '.' in part and part.replace('.', '').replace('-', '').isdigit():
                                            print(f"从Chrome命令行获取版本: {part}")
                                            return part
                        except Exception as e:
                            print(f"Chrome命令行检测失败: {e}")
                            pass
                    
            return None
        except Exception as e:
            print(f"获取Chrome版本时发生错误: {e}")
            return None
    
    def get_chromedriver_version(self, chrome_version: str) -> str:
        """根据Chrome版本获取匹配的ChromeDriver版本"""
        try:
            major_version = chrome_version.split('.')[0]
            print(f"Chrome主版本号: {major_version}")
            
            # Chrome 115+使用新的下载地址和API
            if int(major_version) >= 115:
                # 方法1: 尝试获取指定主版本的最新版本
                try:
                    url = f"https://googlechromelabs.github.io/chrome-for-testing/latest-versions-per-milestone-with-downloads.json"
                    print(f"正在从新API获取ChromeDriver版本信息...")
                    response = requests.get(url, timeout=15)
                    response.raise_for_status()
                    data = response.json()
                    
                    if major_version in data['milestones']:
                        milestone_data = data['milestones'][major_version]
                        version = milestone_data['version']
                        print(f"找到匹配的ChromeDriver版本: {version}")
                        return version
                    else:
                        print(f"未找到主版本 {major_version} 的ChromeDriver")
                except Exception as e:
                    print(f"从新API获取版本失败: {e}")
                
                # 使用已知的版本映射
                version_mapping = {
                    "139": "139.0.6778.85",
                    "138": "138.0.6961.69", 
                    "137": "137.0.6864.75",
                    "136": "136.0.6776.72",
                    "135": "135.0.6790.75"
                }
                
                if major_version in version_mapping:
                    fallback_version = version_mapping[major_version]
                    print(f"使用预设版本映射: {fallback_version}")
                    return fallback_version
            
        except Exception as e:
            print(f"获取ChromeDriver版本失败: {e}")
        
        # 最终回退版本
        fallback_version = "119.0.6045.105"
        print(f"使用回退版本: {fallback_version}")
        return fallback_version

def main():
    """主测试函数"""
    print("=" * 60)
    print("Chrome浏览器版本检测测试")
    print("=" * 60)
    
    # 创建管理器实例
    manager = SimpleWebDriverManager()
    
    # 测试Chrome版本检测
    print("\n1. 检测Chrome版本...")
    chrome_version = manager.get_chrome_version()
    
    if chrome_version:
        print(f"✓ 成功检测到Chrome版本: {chrome_version}")
        major_version = chrome_version.split('.')[0]
        print(f"✓ Chrome主版本号: {major_version}")
        
        # 测试ChromeDriver版本匹配
        print(f"\n2. 获取匹配的ChromeDriver版本...")
        driver_version = manager.get_chromedriver_version(chrome_version)
        print(f"✓ 匹配的ChromeDriver版本: {driver_version}")
        
        # 检查现有ChromeDriver
        print(f"\n3. 检查现有ChromeDriver...")
        chromedriver_name = "chromedriver.exe" if manager.system == "windows" else "chromedriver"
        local_path = manager.drivers_dir / chromedriver_name
        
        if local_path.exists():
            print(f"✓ 发现现有ChromeDriver: {local_path}")
            
            # 尝试获取现有版本信息
            try:
                import subprocess
                startupinfo = None
                creationflags = 0
                if platform.system() == "Windows":
                    if hasattr(subprocess, 'STARTUPINFO'):
                        startupinfo = subprocess.STARTUPINFO()
                        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                        startupinfo.wShowWindow = subprocess.SW_HIDE
                    if hasattr(subprocess, 'CREATE_NO_WINDOW'):
                        creationflags = subprocess.CREATE_NO_WINDOW
                
                result = subprocess.run([
                    str(local_path), '--version'
                ], capture_output=True, text=True, timeout=10,
                   startupinfo=startupinfo, creationflags=creationflags)
                
                if result.returncode == 0 and result.stdout:
                    driver_output = result.stdout.strip()
                    print(f"✓ 现有ChromeDriver版本: {driver_output}")
                    
                    # 检查版本兼容性
                    for part in driver_output.split():
                        if '.' in part and part.replace('.', '').isdigit():
                            existing_version = part
                            existing_major = existing_version.split('.')[0]
                            
                            if existing_major == major_version:
                                print(f"✓ 版本兼容: Chrome {major_version} <-> ChromeDriver {existing_major}")
                                print("\n🎉 Chrome浏览器兼容性检查通过！无需更新ChromeDriver。")
                                return True
                            else:
                                print(f"⚠️  版本不兼容: Chrome {major_version} <-> ChromeDriver {existing_major}")
                                print("需要更新ChromeDriver以匹配当前Chrome版本。")
                                break
                else:
                    print("⚠️  无法获取现有ChromeDriver版本信息")
            except Exception as e:
                print(f"⚠️  检查现有ChromeDriver时出错: {e}")
        else:
            print("! 未发现现有ChromeDriver，需要下载")
        
        print(f"\n建议操作:")
        print(f"- 当前Chrome版本: {chrome_version}")
        print(f"- 需要ChromeDriver版本: {driver_version}")
        print(f"- 程序将自动下载匹配的ChromeDriver版本")
        
        return True
        
    else:
        print("✗ 无法检测到Chrome版本")
        print("请确保已安装Google Chrome浏览器")
        return False

if __name__ == "__main__":
    try:
        success = main()
        print("\n" + "=" * 60)
        if success:
            print("测试完成！Chrome版本检测功能正常。")
        else:
            print("测试失败！请检查Chrome浏览器安装。")
        print("=" * 60)
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        input("\n按回车键退出...")
