#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
无控制台加密打包脚本
集成PyArmor加密和无控制台打包功能
专门解决打包后exe弹出黑框框的问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

# 项目根目录
ROOT_DIR = Path(__file__).parent.absolute()
DIST_DIR = ROOT_DIR / "dist_no_console"
BUILD_DIR = ROOT_DIR / "build_no_console"
ENCRYPTED_DIR = ROOT_DIR / "encrypted_temp"

# 需要加密的模块
MODULES_TO_ENCRYPT = [
    "src/core",
    "src/ui",
    "src/__init__.py",
    "main.py"
]

# 需要复制的资源文件
RESOURCES_TO_COPY = [
    "drivers",
    "config.json",
    "requirements.txt",
    "README.md",
    "cookie_guide.md",
    "chrome_automation_guide.md",
    "logo.ico"
]

def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")

    dirs_to_clean = [DIST_DIR, BUILD_DIR, ENCRYPTED_DIR]
    for dir_path in dirs_to_clean:
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"  已删除: {dir_path}")

    # 删除spec文件
    spec_files = list(ROOT_DIR.glob("*NoConsole*.spec"))
    for spec_file in spec_files:
        spec_file.unlink()
        print(f"  已删除: {spec_file}")

def check_dependencies():
    """检查依赖工具"""
    print("🔍 检查依赖工具...")

    # 检查PyArmor
    try:
        result = subprocess.run(['pyarmor', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"  ✅ PyArmor: {result.stdout.strip()}")
        else:
            print("  ❌ PyArmor 未安装或不可用")
            print("     请运行: pip install pyarmor")
            return False
    except FileNotFoundError:
        print("  ❌ PyArmor 未安装")
        print("     请运行: pip install pyarmor")
        return False

    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"  ✅ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("  ❌ PyInstaller未安装")
        print("     请运行: pip install pyinstaller")
        return False

    return True

def encrypt_modules():
    """使用PyArmor加密模块"""
    print("🔐 使用PyArmor加密模块...")

    # 创建加密输出目录
    ENCRYPTED_DIR.mkdir(exist_ok=True)

    success_count = 0
    total_count = len(MODULES_TO_ENCRYPT)

    for module in MODULES_TO_ENCRYPT:
        module_path = ROOT_DIR / module
        if not module_path.exists():
            print(f"  ⚠️  模块 {module} 不存在，跳过")
            continue

        print(f"  🔒 加密: {module}")

        if module_path.is_dir():
            # 目录加密
            if module == "src/core":
                output_dir = ENCRYPTED_DIR / "src" / "core"
            elif module == "src/ui":
                output_dir = ENCRYPTED_DIR / "src" / "ui"
            else:
                output_dir = ENCRYPTED_DIR / module

            output_dir.parent.mkdir(parents=True, exist_ok=True)

            cmd = [
                "pyarmor", "gen",
                "-O", str(output_dir),
                "-r",
                "--platform", "windows.x86_64",
                str(module_path)
            ]
        else:
            # 文件加密
            if module == "main.py":
                output_dir = ENCRYPTED_DIR
            else:
                output_dir = ENCRYPTED_DIR / module_path.parent
                output_dir.mkdir(parents=True, exist_ok=True)

            cmd = [
                "pyarmor", "gen",
                "-O", str(output_dir),
                "--platform", "windows.x86_64",
                str(module_path)
            ]

        # 执行加密命令
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=ROOT_DIR,
                encoding='utf-8',
                errors='ignore'
            )
            if result.returncode != 0:
                print(f"    ❌ 加密失败: {result.stderr if result.stderr else '未知错误'}")
                print(f"    命令: {' '.join(cmd)}")
                return False
            else:
                print(f"    ✅ 加密成功")
                success_count += 1
        except Exception as e:
            print(f"    ❌ 加密异常: {e}")
            return False

    print(f"  ✅ 加密完成: {success_count}/{total_count} 个模块成功")
    return success_count > 0

def fix_nested_directories():
    """修复PyArmor创建的嵌套目录结构"""
    print("🔧 修复嵌套目录结构...")

    # 修复ui目录
    ui_nested = ENCRYPTED_DIR / "src" / "ui" / "ui"
    if ui_nested.exists():
        ui_temp = ENCRYPTED_DIR / "src" / "ui_temp"
        shutil.move(str(ui_nested), str(ui_temp))
        shutil.rmtree(str(ENCRYPTED_DIR / "src" / "ui"))
        shutil.move(str(ui_temp), str(ENCRYPTED_DIR / "src" / "ui"))
        print("  ✅ 已修复ui目录结构")

    # 修复core目录
    core_nested = ENCRYPTED_DIR / "src" / "core" / "core"
    if core_nested.exists():
        core_temp = ENCRYPTED_DIR / "src" / "core_temp"
        shutil.move(str(core_nested), str(core_temp))
        shutil.rmtree(str(ENCRYPTED_DIR / "src" / "core"))
        shutil.move(str(core_temp), str(ENCRYPTED_DIR / "src" / "core"))
        print("  ✅ 已修复core目录结构")

def copy_resources():
    """复制资源文件到加密目录"""
    print("📁 复制资源文件...")

    for resource in RESOURCES_TO_COPY:
        src_path = ROOT_DIR / resource
        dst_path = ENCRYPTED_DIR / resource

        if not src_path.exists():
            print(f"  ⚠️  资源 {resource} 不存在，跳过")
            continue

        if src_path.is_dir():
            if dst_path.exists():
                shutil.rmtree(dst_path)
            shutil.copytree(src_path, dst_path)
            print(f"  ✅ 已复制目录: {resource}")
        elif src_path.is_file():
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(src_path, dst_path)
            print(f"  ✅ 已复制文件: {resource}")

def create_spec_file():
    """创建专门的无控制台加密spec文件"""
    print("📄 创建无控制台加密spec文件...")

    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
import os

# 加密后的项目目录
ENCRYPTED_DIR = r"{ENCRYPTED_DIR}"

a = Analysis(
    [r"{ENCRYPTED_DIR / 'main.py'}"],
    pathex=[str(ENCRYPTED_DIR)],
    binaries=[],
    datas=[
        (r"{ENCRYPTED_DIR / 'drivers'}", 'drivers'),
        (r"{ENCRYPTED_DIR / 'config.json'}", '.'),
        (r"{ENCRYPTED_DIR / 'src'}", 'src'),
        (r"{ENCRYPTED_DIR / 'logo.ico'}", '.'),
        (r"{ENCRYPTED_DIR / 'pyarmor_runtime_000000'}", 'pyarmor_runtime_000000'),
    ],
    hiddenimports=[
        'PySide6.QtCore',
        'PySide6.QtWidgets', 
        'PySide6.QtGui',
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.chrome',
        'selenium.webdriver.chrome.service',
        'selenium.webdriver.chrome.options',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support.expected_conditions',
        'selenium.common.exceptions',
        'src',
        'src.core',
        'src.core.fanqie_uploader',
        'src.core.novel_parser',
        'src.core.webdriver_manager',
        'src.core.config_manager',
        'src.core.license_manager',
        'src.ui',
        'src.ui.main_window',
        'src.ui.book_info_widget',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='FanqieBookUpload',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # 禁用UPX压缩
    upx_exclude=[],
    console=False,  # 关键：设置为False创建无控制台GUI应用
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=r"{ENCRYPTED_DIR / 'logo.ico'}",
    # Windows特定选项
    version_file=None,
    uac_admin=False,
    uac_uiaccess=False,
)
'''

    spec_path = ROOT_DIR / "FanqieBookUpload_NoConsole_Encrypted.spec"
    with open(spec_path, 'w', encoding='utf-8') as f:
        f.write(spec_content)

    print(f"  ✅ 加密Spec文件已创建: {spec_path}")
    return spec_path

def build_exe(spec_path):
    """使用PyInstaller构建加密的无控制台exe"""
    print("🔨 开始构建加密的无控制台exe...")

    # 确保输出目录存在
    DIST_DIR.mkdir(exist_ok=True)
    BUILD_DIR.mkdir(exist_ok=True)

    # 构建命令
    cmd = [
        "pyinstaller",
        "--distpath", str(DIST_DIR),
        "--workpath", str(BUILD_DIR),
        "--clean",
        str(spec_path)
    ]

    print(f"  🔧 执行命令: {' '.join(cmd)}")

    try:
        # 设置环境变量，减少输出
        env = os.environ.copy()
        env['PYTHONUNBUFFERED'] = '1'

        # 切换到加密目录执行构建
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=ENCRYPTED_DIR,  # 在加密目录中执行
            env=env,
            encoding='utf-8',
            errors='ignore',
            timeout=600  # 10分钟超时，加密构建需要更长时间
        )

        if result.returncode != 0:
            print(f"  ❌ 构建失败:")
            if result.stderr:
                print(f"     错误: {result.stderr}")
            if result.stdout:
                print(f"     输出: {result.stdout}")
            return False
        else:
            print("  ✅ 构建成功")
            return True

    except subprocess.TimeoutExpired:
        print("  ❌ 构建超时")
        return False
    except Exception as e:
        print(f"  ❌ 构建异常: {e}")
        return False

def verify_build():
    """验证构建结果"""
    print("🔍 验证构建结果...")
    
    exe_path = DIST_DIR / "FanqieBookUpload.exe"
    if not exe_path.exists():
        print(f"  ❌ 未找到exe文件: {exe_path}")
        return False
    
    # 检查文件大小
    file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
    print(f"  📦 exe文件大小: {file_size:.1f} MB")
    
    if file_size < 10:
        print("  ⚠️  文件大小异常小，可能构建不完整")
        return False
    
    print(f"  ✅ exe文件创建成功: {exe_path}")
    return True

def create_startup_script():
    """创建启动脚本"""
    print("📝 创建启动脚本...")
    
    # 创建批处理启动脚本
    bat_content = '''@echo off
title 番茄小说上传工具
cd /d "%~dp0"

echo 正在启动番茄小说上传工具...
if exist "FanqieBookUpload.exe" (
    start "" "FanqieBookUpload.exe"
    echo 程序已启动
) else (
    echo 错误：未找到 FanqieBookUpload.exe
    pause
)
'''
    
    bat_path = DIST_DIR / "启动程序.bat"
    with open(bat_path, 'w', encoding='gbk') as f:
        f.write(bat_content)
    
    print(f"  ✅ 启动脚本已创建: {bat_path}")

def cleanup_temp_files():
    """清理临时文件"""
    print("🧹 清理临时文件...")

    if ENCRYPTED_DIR.exists():
        shutil.rmtree(ENCRYPTED_DIR)
        print(f"  ✅ 已删除临时加密目录: {ENCRYPTED_DIR}")

def main():
    """主函数"""
    print("=" * 70)
    print("番茄小说上传工具 - PyArmor加密无控制台打包")
    print("=" * 70)

    # 检查主程序文件
    main_py = ROOT_DIR / "main.py"
    if not main_py.exists():
        print(f"❌ 未找到主程序文件: {main_py}")
        sys.exit(1)

    try:
        # 1. 检查依赖工具
        if not check_dependencies():
            print("❌ 依赖检查失败")
            sys.exit(1)

        # 2. 清理构建目录
        clean_build()

        # 3. 使用PyArmor加密模块
        if not encrypt_modules():
            print("❌ 模块加密失败")
            sys.exit(1)

        # 4. 修复嵌套目录结构
        fix_nested_directories()

        # 5. 复制资源文件
        copy_resources()

        # 6. 创建spec文件
        spec_path = create_spec_file()

        # 7. 构建加密exe
        if not build_exe(spec_path):
            print("❌ 构建失败")
            cleanup_temp_files()
            sys.exit(1)

        # 8. 验证构建结果
        if not verify_build():
            print("❌ 构建验证失败")
            cleanup_temp_files()
            sys.exit(1)

        # 9. 创建启动脚本
        create_startup_script()

        # 10. 清理临时文件
        cleanup_temp_files()

        print("\n" + "=" * 70)
        print("🎉 PyArmor加密无控制台打包完成!")
        print("=" * 70)
        print(f"📁 输出目录: {DIST_DIR}")
        print(f"🚀 可执行文件: {DIST_DIR / 'FanqieBookUpload.exe'}")
        print(f"📜 启动脚本: {DIST_DIR / '启动程序.bat'}")
        print("\n💡 特性说明:")
        print("  ✅ 代码已使用PyArmor加密保护")
        print("  ✅ 无控制台窗口，不会弹出黑框框")
        print("  ✅ 单文件exe，便于分发")
        print("  ✅ 包含所有必要的资源文件")
        print("\n🚀 使用方法:")
        print("  - 直接运行 FanqieBookUpload.exe")
        print("  - 或者双击 启动程序.bat")
        print("=" * 70)

    except KeyboardInterrupt:
        print("\n❌ 用户中断构建")
        cleanup_temp_files()
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 构建过程出现异常: {e}")
        cleanup_temp_files()
        sys.exit(1)

if __name__ == "__main__":
    main()
