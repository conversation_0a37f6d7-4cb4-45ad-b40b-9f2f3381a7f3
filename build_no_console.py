#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
无控制台打包脚本
专门解决打包后exe弹出黑框框的问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

# 项目根目录
ROOT_DIR = Path(__file__).parent.absolute()
DIST_DIR = ROOT_DIR / "dist"
BUILD_DIR = ROOT_DIR / "build"

def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = [DIST_DIR, BUILD_DIR]
    for dir_path in dirs_to_clean:
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"  已删除: {dir_path}")
    
    # 删除spec文件
    spec_files = list(ROOT_DIR.glob("*.spec"))
    for spec_file in spec_files:
        spec_file.unlink()
        print(f"  已删除: {spec_file}")

def create_spec_file():
    """创建专门的无控制台spec文件"""
    print("📄 创建无控制台spec文件...")
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
import os

# 项目根目录
ROOT_DIR = r"{ROOT_DIR}"

a = Analysis(
    [r"{ROOT_DIR / 'main.py'}"],
    pathex=[ROOT_DIR],
    binaries=[],
    datas=[
        (r"{ROOT_DIR / 'drivers'}", 'drivers'),
        (r"{ROOT_DIR / 'config.json'}", '.'),
        (r"{ROOT_DIR / 'src'}", 'src'),
        (r"{ROOT_DIR / 'logo.ico'}", '.'),
    ],
    hiddenimports=[
        'PySide6.QtCore',
        'PySide6.QtWidgets', 
        'PySide6.QtGui',
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.chrome',
        'selenium.webdriver.chrome.service',
        'selenium.webdriver.chrome.options',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support.expected_conditions',
        'selenium.common.exceptions',
        'src',
        'src.core',
        'src.core.fanqie_uploader',
        'src.core.novel_parser',
        'src.core.webdriver_manager',
        'src.core.config_manager',
        'src.core.license_manager',
        'src.ui',
        'src.ui.main_window',
        'src.ui.book_info_widget',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='FanqieBookUpload',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # 禁用UPX压缩
    upx_exclude=[],
    console=False,  # 关键：设置为False创建无控制台GUI应用
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=r"{ROOT_DIR / 'logo.ico'}",
    # Windows特定选项
    version_file=None,
    uac_admin=False,
    uac_uiaccess=False,
)
'''
    
    spec_path = ROOT_DIR / "FanqieBookUpload_NoConsole.spec"
    with open(spec_path, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print(f"  ✅ Spec文件已创建: {spec_path}")
    return spec_path

def build_exe(spec_path):
    """使用PyInstaller构建exe"""
    print("🔨 开始构建无控制台exe...")
    
    # 确保输出目录存在
    DIST_DIR.mkdir(exist_ok=True)
    
    # 构建命令
    cmd = [
        "pyinstaller",
        "--distpath", str(DIST_DIR),
        "--workpath", str(BUILD_DIR),
        "--clean",
        str(spec_path)
    ]
    
    print(f"  执行命令: {' '.join(cmd)}")
    
    try:
        # 设置环境变量，减少输出
        env = os.environ.copy()
        env['PYTHONUNBUFFERED'] = '1'
        
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            cwd=ROOT_DIR,
            env=env,
            timeout=300  # 5分钟超时
        )
        
        if result.returncode != 0:
            print(f"  ❌ 构建失败:")
            if result.stderr:
                print(f"     错误: {result.stderr}")
            if result.stdout:
                print(f"     输出: {result.stdout}")
            return False
        else:
            print("  ✅ 构建成功")
            return True
            
    except subprocess.TimeoutExpired:
        print("  ❌ 构建超时")
        return False
    except Exception as e:
        print(f"  ❌ 构建异常: {e}")
        return False

def verify_build():
    """验证构建结果"""
    print("🔍 验证构建结果...")
    
    exe_path = DIST_DIR / "FanqieBookUpload.exe"
    if not exe_path.exists():
        print(f"  ❌ 未找到exe文件: {exe_path}")
        return False
    
    # 检查文件大小
    file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
    print(f"  📦 exe文件大小: {file_size:.1f} MB")
    
    if file_size < 10:
        print("  ⚠️  文件大小异常小，可能构建不完整")
        return False
    
    print(f"  ✅ exe文件创建成功: {exe_path}")
    return True

def create_startup_script():
    """创建启动脚本"""
    print("📝 创建启动脚本...")
    
    # 创建批处理启动脚本
    bat_content = '''@echo off
title 番茄小说上传工具
cd /d "%~dp0"

echo 正在启动番茄小说上传工具...
if exist "FanqieBookUpload.exe" (
    start "" "FanqieBookUpload.exe"
    echo 程序已启动
) else (
    echo 错误：未找到 FanqieBookUpload.exe
    pause
)
'''
    
    bat_path = DIST_DIR / "启动程序.bat"
    with open(bat_path, 'w', encoding='gbk') as f:
        f.write(bat_content)
    
    print(f"  ✅ 启动脚本已创建: {bat_path}")

def main():
    """主函数"""
    print("=" * 60)
    print("番茄小说上传工具 - 无控制台打包")
    print("=" * 60)
    
    # 检查依赖
    try:
        import PyInstaller
        print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller未安装，请运行: pip install pyinstaller")
        sys.exit(1)
    
    # 检查主程序文件
    main_py = ROOT_DIR / "main.py"
    if not main_py.exists():
        print(f"❌ 未找到主程序文件: {main_py}")
        sys.exit(1)
    
    try:
        # 1. 清理构建目录
        clean_build()
        
        # 2. 创建spec文件
        spec_path = create_spec_file()
        
        # 3. 构建exe
        if not build_exe(spec_path):
            print("❌ 构建失败")
            sys.exit(1)
        
        # 4. 验证构建结果
        if not verify_build():
            print("❌ 构建验证失败")
            sys.exit(1)
        
        # 5. 创建启动脚本
        create_startup_script()
        
        print("\n" + "=" * 60)
        print("🎉 无控制台打包完成!")
        print("=" * 60)
        print(f"📁 输出目录: {DIST_DIR}")
        print(f"🚀 可执行文件: {DIST_DIR / 'FanqieBookUpload.exe'}")
        print(f"📜 启动脚本: {DIST_DIR / '启动程序.bat'}")
        print("\n💡 使用说明:")
        print("  - 直接运行 FanqieBookUpload.exe")
        print("  - 或者双击 启动程序.bat")
        print("  - 打包后的程序不会弹出黑框框")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断构建")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 构建过程出现异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
