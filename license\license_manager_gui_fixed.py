#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
许可证管理器 - GUI版本 (无控制台输出优化版)
图形化界面用于创建和管理许可证
"""

import sys
import os
import io

# 重定向所有输出以防止控制台窗口弹出
if hasattr(sys, 'frozen'):
    # 当打包为exe时，重定向标准输出和错误
    sys.stdout = io.StringIO()
    sys.stderr = io.StringIO()

from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QGridLayout, QLabel, QLineEdit, QPushButton, QTextEdit, QTableWidget,
    QTableWidgetItem, QSpinBox, QMessageBox, QTabWidget, QGroupBox,
    QHeaderView, QComboBox, QDateEdit, QPlainTextEdit, QSplitter,
    QFileDialog, QProgressBar
)
from PySide6.QtCore import Qt, QDate, QThread, Signal
from PySide6.QtGui import QFont, QIcon, QPixmap

from machine_code_generator import MachineCodeGenerator


class LicenseCreationThread(QThread):
    """许可证创建线程"""
    finished = Signal(dict, str)  # 结果, 消息
    
    def __init__(self, generator, machine_code, user_name, email, expire_days, max_devices, notes):
        super().__init__()
        self.generator = generator
        self.machine_code = machine_code
        self.user_name = user_name
        self.email = email
        self.expire_days = expire_days
        self.max_devices = max_devices
        self.notes = notes
    
    def run(self):
        try:
            result, message = self.generator.create_license_for_machine(
                self.machine_code, self.user_name, self.email, 
                self.expire_days, self.max_devices, self.notes
            )
            self.finished.emit(result, message)
        except Exception as e:
            self.finished.emit(None, f"创建许可证时发生错误: {str(e)}")


class LicenseManagerGUI(QMainWindow):
    """许可证管理器GUI主窗口"""
    
    def __init__(self):
        super().__init__()
        self.generator = MachineCodeGenerator()
        self.creation_thread = None
        
        self.init_ui()
        self.load_licenses()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("番茄小说上传工具 - 许可证管理器 v2.0")
        self.setGeometry(100, 100, 1000, 700)
        
        # 设置应用图标
        icon_path = os.path.join(os.path.dirname(__file__), 'logo.ico')
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标签页容器
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建各个标签页
        self.create_license_tab()
        self.create_management_tab()
        self.create_info_tab()
    
    def create_license_tab(self):
        """创建许可证创建标签页"""
        license_widget = QWidget()
        layout = QVBoxLayout(license_widget)
        
        # 标题
        title_label = QLabel("🎫 创建新许可证")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：创建表单
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 用户信息组
        user_group = QGroupBox("用户信息")
        user_layout = QGridLayout(user_group)
        
        user_layout.addWidget(QLabel("机器码:"), 0, 0)
        self.machine_code_edit = QLineEdit()
        self.machine_code_edit.setPlaceholderText("请输入16位机器码（如：79C626613F403CB8）")
        self.machine_code_edit.setStyleSheet("""
            QLineEdit {
                font-family: 'Courier New', monospace;
                font-size: 13px;
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 4px;
                padding: 8px;
            }
            QLineEdit:focus {
                border-color: #007bff;
                background-color: white;
            }
        """)
        user_layout.addWidget(self.machine_code_edit, 0, 1)
        
        user_layout.addWidget(QLabel("用户名:"), 1, 0)
        self.user_name_edit = QLineEdit()
        self.user_name_edit.setPlaceholderText("请输入用户名")
        user_layout.addWidget(self.user_name_edit, 1, 1)
        
        user_layout.addWidget(QLabel("邮箱:"), 2, 0)
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("请输入邮箱地址（可选）")
        user_layout.addWidget(self.email_edit, 2, 1)
        
        left_layout.addWidget(user_group)
        
        # 许可证设置组
        license_group = QGroupBox("许可证设置")
        license_layout = QGridLayout(license_group)
        
        license_layout.addWidget(QLabel("有效期(天):"), 0, 0)
        self.expire_days_spin = QSpinBox()
        self.expire_days_spin.setRange(1, 3650)
        self.expire_days_spin.setValue(365)
        self.expire_days_spin.setSuffix(" 天")
        license_layout.addWidget(self.expire_days_spin, 0, 1)
        
        license_layout.addWidget(QLabel("最大设备数:"), 1, 0)
        self.max_devices_spin = QSpinBox()
        self.max_devices_spin.setRange(1, 10)
        self.max_devices_spin.setValue(1)
        self.max_devices_spin.setSuffix(" 台")
        license_layout.addWidget(self.max_devices_spin, 1, 1)
        
        license_layout.addWidget(QLabel("备注:"), 2, 0)
        self.notes_edit = QLineEdit()
        self.notes_edit.setPlaceholderText("可选的备注信息")
        license_layout.addWidget(self.notes_edit, 2, 1)
        
        left_layout.addWidget(license_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.create_btn = QPushButton("🎫 创建许可证")
        self.create_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        self.create_btn.clicked.connect(self.create_license)
        button_layout.addWidget(self.create_btn)
        
        self.clear_btn = QPushButton("🗑️ 清空表单")
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        self.clear_btn.clicked.connect(self.clear_form)
        button_layout.addWidget(self.clear_btn)
        
        button_layout.addStretch()
        left_layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        left_layout.addWidget(self.progress_bar)
        
        left_layout.addStretch()
        splitter.addWidget(left_widget)
        
        # 右侧：结果显示
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        result_group = QGroupBox("创建结果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_text = QPlainTextEdit()
        self.result_text.setPlaceholderText("许可证创建结果将在此显示...")
        self.result_text.setReadOnly(True)
        result_layout.addWidget(self.result_text)
        
        # 结果操作按钮
        result_button_layout = QHBoxLayout()
        
        self.copy_result_btn = QPushButton("📋 复制结果")
        self.copy_result_btn.clicked.connect(self.copy_result)
        self.copy_result_btn.setEnabled(False)
        result_button_layout.addWidget(self.copy_result_btn)
        
        self.save_result_btn = QPushButton("💾 保存文件")
        self.save_result_btn.clicked.connect(self.save_result)
        self.save_result_btn.setEnabled(False)
        result_button_layout.addWidget(self.save_result_btn)
        
        result_button_layout.addStretch()
        result_layout.addLayout(result_button_layout)
        
        right_layout.addWidget(result_group)
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 1)
        
        # 添加到标签页
        self.tab_widget.addTab(license_widget, "🎫 创建许可证")
    
    def create_management_tab(self):
        """创建许可证管理标签页"""
        management_widget = QWidget()
        layout = QVBoxLayout(management_widget)
        
        # 标题
        title_label = QLabel("📊 许可证管理")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 操作按钮区域
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("🔄 刷新列表")
        self.refresh_btn.clicked.connect(self.load_licenses)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        button_layout.addWidget(self.refresh_btn)
        
        self.deactivate_btn = QPushButton("❌ 停用许可证")
        self.deactivate_btn.clicked.connect(self.deactivate_license)
        self.deactivate_btn.setEnabled(False)
        self.deactivate_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        button_layout.addWidget(self.deactivate_btn)
        
        self.export_btn = QPushButton("📤 导出数据")
        self.export_btn.clicked.connect(self.export_licenses)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        button_layout.addWidget(self.export_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 许可证列表表格
        self.license_table = QTableWidget()
        self.license_table.setColumnCount(9)
        self.license_table.setHorizontalHeaderLabels([
            "ID", "用户名", "邮箱", "机器码", "许可证密钥", 
            "创建时间", "过期时间", "状态", "备注"
        ])
        
        # 设置表格属性
        header = self.license_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        header.setSectionResizeMode(4, QHeaderView.Stretch)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(8, QHeaderView.Stretch)
        
        self.license_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.license_table.itemSelectionChanged.connect(self.on_license_selected)
        
        layout.addWidget(self.license_table)
        
        # 统计信息
        stats_layout = QHBoxLayout()
        self.stats_label = QLabel("总计: 0 个许可证 | 有效: 0 个 | 停用: 0 个")
        self.stats_label.setStyleSheet("color: #7f8c8d; font-weight: bold;")
        stats_layout.addWidget(self.stats_label)
        stats_layout.addStretch()
        layout.addLayout(stats_layout)
        
        # 添加到标签页
        self.tab_widget.addTab(management_widget, "📊 许可证管理")
    
    def create_info_tab(self):
        """创建系统信息标签页"""
        info_widget = QWidget()
        layout = QVBoxLayout(info_widget)
        
        # 标题
        title_label = QLabel("ℹ️ 系统信息")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 当前机器信息
        machine_group = QGroupBox("当前机器信息")
        machine_layout = QVBoxLayout(machine_group)
        
        self.machine_info_text = QPlainTextEdit()
        self.machine_info_text.setReadOnly(True)
        self.machine_info_text.setMaximumHeight(200)
        machine_layout.addWidget(self.machine_info_text)
        
        # 获取机器信息按钮
        get_info_btn = QPushButton("🔍 获取机器信息")
        get_info_btn.clicked.connect(self.get_machine_info)
        machine_layout.addWidget(get_info_btn)
        
        layout.addWidget(machine_group)
        
        # 软件信息
        software_group = QGroupBox("软件信息")
        software_layout = QGridLayout(software_group)
        
        software_layout.addWidget(QLabel("软件名称:"), 0, 0)
        software_layout.addWidget(QLabel("番茄小说上传工具 - 许可证管理器"), 0, 1)
        
        software_layout.addWidget(QLabel("版本号:"), 1, 0)
        software_layout.addWidget(QLabel("v2.0.1"), 1, 1)
        
        software_layout.addWidget(QLabel("开发者:"), 2, 0)
        software_layout.addWidget(QLabel("AI助手"), 2, 1)
        
        software_layout.addWidget(QLabel("数据库:"), 3, 0)
        db_path = os.path.abspath(self.generator.db_path)
        software_layout.addWidget(QLabel(db_path), 3, 1)
        
        # 本机机器码
        software_layout.addWidget(QLabel("本机机器码:"), 4, 0)
        machine_code = self.generator.generate_machine_code()
        machine_code_label = QLabel(machine_code or "获取失败")
        machine_code_label.setStyleSheet("""
            QLabel {
                font-family: 'Courier New', monospace;
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                padding: 4px 8px;
            }
        """)
        software_layout.addWidget(machine_code_label, 4, 1)
        
        # 许可证状态
        software_layout.addWidget(QLabel("许可证状态:"), 5, 0)
        license_status_label = self.get_license_status_label()
        software_layout.addWidget(license_status_label, 5, 1)
        
        layout.addWidget(software_group)
        
        # 使用说明
        usage_group = QGroupBox("使用说明")
        usage_layout = QVBoxLayout(usage_group)
        
        usage_text = QPlainTextEdit()
        usage_text.setPlainText(
            "📋 许可证管理器使用说明：\n\n"
            "1. 创建许可证：\n"
            "   • 在'创建许可证'标签页填写用户信息\n"
            "   • 设置有效期和最大设备数\n"
            "   • 点击'创建许可证'按钮\n"
            "   • 复制或保存生成的许可证信息\n\n"
            "2. 管理许可证：\n"
            "   • 在'许可证管理'标签页查看所有许可证\n"
            "   • 选择许可证后可以停用\n"
            "   • 支持导出许可证数据\n\n"
            "3. 注意事项：\n"
            "   • 每个机器码只能创建一个许可证\n"
            "   • 停用的许可证无法恢复\n"
            "   • 定期备份数据库文件\n"
            "   • 妥善保管许可证密钥"
        )
        usage_text.setReadOnly(True)
        usage_layout.addWidget(usage_text)
        
        layout.addWidget(usage_group)
        
        layout.addStretch()
        
        # 添加到标签页
        self.tab_widget.addTab(info_widget, "ℹ️ 系统信息")
    
    def create_license(self):
        """创建许可证"""
        # 获取表单数据
        machine_code = self.machine_code_edit.text().strip().upper()
        user_name = self.user_name_edit.text().strip()
        email = self.email_edit.text().strip()
        expire_days = self.expire_days_spin.value()
        max_devices = self.max_devices_spin.value()
        notes = self.notes_edit.text().strip()
        
        # 验证必需字段
        if not machine_code:
            QMessageBox.warning(self, "警告", "请输入机器码")
            return
        
        if not user_name:
            QMessageBox.warning(self, "警告", "请输入用户名")
            return
        
        # 验证机器码格式（16位十六进制）
        if len(machine_code) != 16:
            QMessageBox.warning(self, "警告", "机器码必须是16位字符")
            return
        
        try:
            int(machine_code, 16)  # 验证是否为有效的十六进制
        except ValueError:
            QMessageBox.warning(self, "警告", "机器码必须是有效的十六进制字符")
            return
        
        # 禁用创建按钮
        self.create_btn.setEnabled(False)
        self.create_btn.setText("创建中...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        
        # 在线程中创建许可证
        self.creation_thread = LicenseCreationThread(
            self.generator, machine_code, user_name, email, expire_days, max_devices, notes
        )
        self.creation_thread.finished.connect(self.on_license_created)
        self.creation_thread.start()
    
    def on_license_created(self, result, message):
        """许可证创建完成回调"""
        # 恢复UI状态
        self.create_btn.setEnabled(True)
        self.create_btn.setText("🎫 创建许可证")
        self.progress_bar.setVisible(False)
        
        if result:
            # 创建成功
            success_text = (
                f"✅ {message}\n\n"
                f"📋 许可证信息：\n"
                f"用户名: {result['user_name']}\n"
                f"邮箱: {result['email']}\n"
                f"机器码: {result['machine_code']}\n"
                f"许可证密钥: {result['license_key']}\n"
                f"创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"过期时间: {result['expire_time']}\n"
                f"最大设备数: {result['max_devices']}\n"
                f"备注: {self.notes_edit.text().strip()}\n\n"
                f"⚠️ 请将机器码和许可证密钥提供给用户进行激活。"
            )
            
            self.result_text.setPlainText(success_text)
            self.copy_result_btn.setEnabled(True)
            self.save_result_btn.setEnabled(True)
            
            # 保存结果用于复制和保存
            self.current_result = result
            
            QMessageBox.information(self, "成功", "许可证创建成功！")
            
            # 刷新许可证列表
            self.load_licenses()
            
        else:
            # 创建失败
            error_text = f"❌ 创建失败: {message}"
            self.result_text.setPlainText(error_text)
            QMessageBox.critical(self, "错误", message)
    
    def clear_form(self):
        """清空表单"""
        self.machine_code_edit.clear()
        self.user_name_edit.clear()
        self.email_edit.clear()
        self.expire_days_spin.setValue(365)
        self.max_devices_spin.setValue(1)
        self.notes_edit.clear()
        self.result_text.clear()
        self.copy_result_btn.setEnabled(False)
        self.save_result_btn.setEnabled(False)
    
    def copy_result(self):
        """复制结果到剪贴板"""
        try:
            clipboard = QApplication.clipboard()
            clipboard.setText(self.result_text.toPlainText())
            QMessageBox.information(self, "成功", "结果已复制到剪贴板")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"复制失败: {str(e)}")
    
    def save_result(self):
        """保存结果到文件"""
        if not hasattr(self, 'current_result') or not self.current_result:
            return
        
        filename = f"license_{self.current_result['machine_code']}.txt"
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存许可证文件", filename, "文本文件 (*.txt)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.result_text.toPlainText())
                QMessageBox.information(self, "成功", f"许可证信息已保存到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")
    
    def load_licenses(self):
        """加载许可证列表"""
        try:
            licenses = self.generator.list_licenses()
            
            # 清空表格
            self.license_table.setRowCount(0)
            
            # 填充表格
            for license in licenses:
                row = self.license_table.rowCount()
                self.license_table.insertRow(row)
                
                self.license_table.setItem(row, 0, QTableWidgetItem(str(license['id'])))
                self.license_table.setItem(row, 1, QTableWidgetItem(license['user_name'] or ''))
                self.license_table.setItem(row, 2, QTableWidgetItem(license['email'] or ''))
                self.license_table.setItem(row, 3, QTableWidgetItem(license['machine_code']))
                self.license_table.setItem(row, 4, QTableWidgetItem(license['license_key']))
                self.license_table.setItem(row, 5, QTableWidgetItem(license['created_time']))
                self.license_table.setItem(row, 6, QTableWidgetItem(license['expire_time'] or ''))
                
                # 状态显示
                status = "有效" if license['is_active'] else "停用"
                status_item = QTableWidgetItem(status)
                if license['is_active']:
                    status_item.setBackground(Qt.GlobalColor.green)
                else:
                    status_item.setBackground(Qt.GlobalColor.red)
                self.license_table.setItem(row, 7, status_item)
                
                self.license_table.setItem(row, 8, QTableWidgetItem(license['notes'] or ''))
            
            # 更新统计信息
            total = len(licenses)
            active = sum(1 for l in licenses if l['is_active'])
            inactive = total - active
            
            self.stats_label.setText(f"总计: {total} 个许可证 | 有效: {active} 个 | 停用: {inactive} 个")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载许可证列表失败: {str(e)}")
    
    def on_license_selected(self):
        """许可证选择改变"""
        selected_rows = self.license_table.selectionModel().selectedRows()
        self.deactivate_btn.setEnabled(len(selected_rows) > 0)
    
    def deactivate_license(self):
        """停用选中的许可证"""
        selected_rows = self.license_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        row = selected_rows[0].row()
        license_key = self.license_table.item(row, 4).text()
        user_name = self.license_table.item(row, 1).text()
        
        reply = QMessageBox.question(
            self, "确认停用", 
            f"确定要停用用户 '{user_name}' 的许可证吗？\n\n"
            f"许可证密钥: {license_key}\n\n"
            f"⚠️ 此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                if self.generator.deactivate_license(license_key):
                    QMessageBox.information(self, "成功", "许可证已停用")
                    self.load_licenses()  # 刷新列表
                else:
                    QMessageBox.warning(self, "失败", "停用许可证失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"停用许可证时发生错误: {str(e)}")
    
    def export_licenses(self):
        """导出许可证数据"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出许可证数据", "licenses_export.csv", "CSV文件 (*.csv)"
        )
        
        if file_path:
            try:
                licenses = self.generator.list_licenses()
                
                with open(file_path, 'w', encoding='utf-8-sig') as f:
                    # 写入标题行
                    f.write("ID,用户名,邮箱,机器码,许可证密钥,创建时间,过期时间,状态,备注\n")
                    
                    # 写入数据行
                    for license in licenses:
                        status = "有效" if license['is_active'] else "停用"
                        f.write(f"{license['id']},{license['user_name'] or ''},"
                               f"{license['email'] or ''},{license['machine_code']},"
                               f"{license['license_key']},{license['created_time']},"
                               f"{license['expire_time'] or ''},{status},"
                               f"{license['notes'] or ''}\n")
                
                QMessageBox.information(self, "成功", f"许可证数据已导出到: {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
    
    def get_machine_info(self):
        """获取当前机器信息"""
        try:
            machine_info = self.generator.get_machine_info()
            machine_code = self.generator.generate_machine_code(machine_info)
            
            info_text = "🖥️ 当前机器信息：\n\n"
            
            if machine_info:
                for key, value in machine_info.items():
                    info_text += f"{key}: {value}\n"
                
                info_text += f"\n🔑 生成的机器码: {machine_code}\n"
            else:
                info_text += "❌ 无法获取机器信息"
            
            self.machine_info_text.setPlainText(info_text)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取机器信息失败: {str(e)}")
    
    def get_license_status_label(self):
        """获取许可证状态标签"""
        try:
            # 获取当前机器码
            machine_code = self.generator.generate_machine_code()
            if not machine_code:
                status_label = QLabel("❌ 无法获取机器码")
                status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
                return status_label
            
            # 查找当前机器的许可证
            licenses = self.generator.list_licenses()
            current_license = None
            for license in licenses:
                if license['machine_code'] == machine_code and license['is_active']:
                    current_license = license
                    break
            
            if current_license:
                # 检查是否过期
                try:
                    expire_time = datetime.strptime(current_license['expire_time'], "%Y-%m-%d %H:%M:%S")
                    created_time = datetime.strptime(current_license['created_time'], "%Y-%m-%d %H:%M:%S")
                    now = datetime.now()
                    
                    if now > expire_time:
                        status_text = f"❌ 已过期\n激活时间: {current_license['created_time']}\n过期时间: {current_license['expire_time']}"
                        status_label = QLabel(status_text)
                        status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
                    else:
                        days_left = (expire_time - now).days
                        status_text = f"✅ 已激活\n激活时间: {current_license['created_time']}\n过期时间: {current_license['expire_time']}\n剩余天数: {days_left} 天"
                        status_label = QLabel(status_text)
                        status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
                except:
                    status_text = f"✅ 已激活\n激活时间: {current_license['created_time']}\n过期时间: {current_license['expire_time']}"
                    status_label = QLabel(status_text)
                    status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
            else:
                status_text = "❌ 未激活\n本机尚未激活许可证"
                status_label = QLabel(status_text)
                status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
            
            status_label.setStyleSheet(status_label.styleSheet() + """
                QLabel {
                    background-color: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 4px;
                    padding: 8px;
                    line-height: 1.4;
                }
            """)
            
            return status_label
            
        except Exception as e:
            status_label = QLabel(f"❌ 获取状态失败: {str(e)}")
            status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
            return status_label


def main():
    """主函数"""
    # 重定向输出（防止控制台窗口）
    if hasattr(sys, 'frozen'):
        sys.stdout = io.StringIO()
        sys.stderr = io.StringIO()
    
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("许可证管理器")
    app.setApplicationVersion("2.0.1")
    app.setOrganizationName("FanqieBookUpload")
    
    # 设置应用程序图标
    icon_path = os.path.join(os.path.dirname(__file__), 'logo.ico')
    if os.path.exists(icon_path):
        app.setWindowIcon(QIcon(icon_path))
    
    # 创建主窗口
    window = LicenseManagerGUI()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
