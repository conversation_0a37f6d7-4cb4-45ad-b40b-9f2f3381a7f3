# -*- mode: python ; coding: utf-8 -*-

import os
import sys

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(SPEC))
sys.path.insert(0, current_dir)

block_cipher = None

# 主程序入口
main_script = os.path.join(current_dir, 'ultimate_no_console.py')

# 数据文件
datas = []

# 添加数据库文件
db_file = os.path.join(current_dir, 'license_db.sqlite')
if os.path.exists(db_file):
    datas.append((db_file, '.'))

# 添加说明文档
readme_file = os.path.join(current_dir, '机器码验证系统使用说明.md')
if os.path.exists(readme_file):
    datas.append((readme_file, '.'))

# 添加图标文件
icon_file = os.path.join(current_dir, 'logo.ico')
if os.path.exists(icon_file):
    datas.append((icon_file, '.'))

# 隐藏导入
hiddenimports = [
    # Python标准库
    'json',
    'sqlite3',
    'hashlib',
    'hmac',
    'base64',
    'platform',
    'uuid',
    'subprocess',
    'datetime',
    'pathlib',
    'os',
    'sys',
    'shutil',
    'threading',
    # PySide6模块
    'PySide6.QtCore',
    'PySide6.QtGui',
    'PySide6.QtWidgets',
    # 加密后的模块
    'machine_code_generator',
    'create_test_license',
    'demo_license_system',
]

a = Analysis(
    [main_script],
    pathex=[current_dir],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='许可证管理器',
    debug=False,
    bootloader_ignore_signals=True,  # 忽略引导程序信号
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # GUI应用，完全禁用控制台
    disable_windowed_traceback=True,  # 禁用窗口化回溯
    argv_emulation=False,  # 禁用参数模拟
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_file if os.path.exists(icon_file) else None,
)
