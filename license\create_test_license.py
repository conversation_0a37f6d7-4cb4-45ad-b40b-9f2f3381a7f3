#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建测试许可证的自动化脚本
"""

import sys
import os
from machine_code_generator import MachineCodeGenerator

def create_test_license():
    """创建测试许可证"""
    print("=== 创建测试许可证 ===")
    
    # 创建生成器实例
    generator = MachineCodeGenerator()
    
    # 创建许可证
    result, message = generator.create_license(
        user_name="测试用户",
        email="<EMAIL>", 
        expire_days=365,
        max_devices=1,
        notes="测试许可证"
    )
    
    if result:
        print(f"✅ {message}")
        print(f"机器码: {result['machine_code']}")
        print(f"许可证密钥: {result['license_key']}")
        print(f"过期时间: {result['expire_time']}")
        
        # 返回许可证信息用于测试
        return result['license_key'], result['machine_code']
    else:
        print(f"❌ 创建失败: {message}")
        return None, None

if __name__ == "__main__":
    license_key, machine_code = create_test_license()
    if license_key:
        print(f"\n测试许可证创建成功！")
        print(f"机器码: {machine_code}")
        print(f"许可证密钥: {license_key}")
        print(f"\n请使用这些信息测试软件激活。")
