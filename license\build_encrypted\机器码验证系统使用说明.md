# 番茄小说上传工具 - 机器码验证系统使用说明

## 📋 系统概述

机器码验证系统是为番茄小说上传工具开发的软件授权机制，确保软件的合法使用和版权保护。

## 🏗️ 系统架构

### 核心组件

1. **机器码生成器** (`machine_code_generator.py`) - 管理端工具
2. **许可证管理器** (`src/core/license_manager.py`) - 客户端验证
3. **主程序集成** (`main.py`) - 启动时验证

### 工作流程

```
用户机器 → 生成机器码 → 提交给管理员 → 生成许可证 → 用户激活 → 软件启动
```

## 🛠️ 管理员操作指南

### 1. 运行机器码生成器

```bash
python machine_code_generator.py
```

### 2. 生成器功能菜单

```
1. 获取当前机器信息    - 查看硬件信息
2. 生成机器码          - 生成当前机器的唯一标识
3. 创建新许可证        - 为用户生成许可证
4. 验证许可证          - 验证许可证有效性
5. 查看所有许可证      - 管理已发放的许可证
6. 停用许可证          - 禁用特定许可证
```

### 3. 创建许可证步骤

1. 选择菜单项 "3. 创建新许可证"
2. 输入用户信息：
   - 用户名（可选）
   - 邮箱（可选）
   - 有效期天数（默认 365 天）
   - 最大设备数（默认 1 台）
   - 备注信息（可选）
3. 系统自动生成：
   - 机器码
   - 许可证密钥
   - 许可证文件（`license_机器码.txt`）

### 4. 许可证管理

- **查看许可证列表**：显示所有已创建的许可证
- **停用许可证**：使特定许可证失效
- **数据库文件**：`license_db.sqlite`

## 👤 用户使用指南

### 1. 首次启动软件

- 软件会自动检测许可证状态
- 如未激活，会显示激活对话框

### 2. 激活流程

1. **获取机器码**

   - 启动软件时自动显示
   - 复制机器码发送给软件提供商

2. **获取许可证密钥**

   - 联系软件提供商
   - 提供机器码以获取对应的许可证密钥

3. **激活软件**
   - 在激活对话框中输入许可证密钥
   - 点击"激活"按钮
   - 激活成功后软件正常启动

### 3. 激活状态说明

- ✅ **已激活**：软件正常启动，无需任何操作
- ❌ **未激活**：显示激活对话框，需要输入许可证密钥
- ⚠️ **机器码不匹配**：硬件发生变化，需要重新激活

## 🔧 技术实现

### 机器码生成算法

```python
# 基于硬件信息生成唯一标识
硬件信息 = {
    'platform', 'processor', 'machine', 'node',
    'system', 'version', 'mac_address',
    'motherboard_serial', 'disk_serial', 'cpu_id'  # Windows
}
机器码 = SHA256(硬件信息)[:16].upper()
```

### 许可证密钥算法

```python
# 使用HMAC-SHA256生成签名
数据 = f"{机器码}:{过期日期}"
签名 = HMAC-SHA256(密钥, 数据)
许可证密钥 = Base64(签名)[:20]
```

### 文件结构

```
├── machine_code_generator.py          # 管理端生成器
├── license_db.sqlite                  # 许可证数据库
├── src/core/license_manager.py        # 客户端管理器
├── main.py                           # 主程序（集成验证）
├── license.json                      # 本地许可证文件
└── license_机器码.txt                # 生成的许可证文件
```

## 🛡️ 安全特性

### 加密保护

- **HMAC-SHA256 签名**：防止许可证伪造
- **机器码绑定**：确保许可证只能在指定机器使用
- **时间验证**：支持许可证过期检查

### 反破解措施

- **硬件指纹**：基于多项硬件信息生成机器码
- **本地验证**：减少网络依赖，提高用户体验
- **PyArmor 加密**：源代码加密保护

## 📊 数据库结构

### licenses 表

```sql
CREATE TABLE licenses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    machine_code TEXT UNIQUE NOT NULL,      -- 机器码
    license_key TEXT UNIQUE NOT NULL,       -- 许可证密钥
    user_name TEXT,                         -- 用户名
    email TEXT,                             -- 邮箱
    created_time TEXT NOT NULL,             -- 创建时间
    expire_time TEXT,                       -- 过期时间
    max_devices INTEGER DEFAULT 1,         -- 最大设备数
    is_active BOOLEAN DEFAULT 1,           -- 是否激活
    last_check_time TEXT,                   -- 最后检查时间
    notes TEXT                              -- 备注
);
```

### device_usage 表

```sql
CREATE TABLE device_usage (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_key TEXT NOT NULL,             -- 许可证密钥
    machine_code TEXT NOT NULL,            -- 机器码
    ip_address TEXT,                       -- IP地址
    last_used_time TEXT NOT NULL,         -- 最后使用时间
    usage_count INTEGER DEFAULT 1         -- 使用次数
);
```

## 🔍 故障排除

### 常见问题

#### 1. 机器码获取失败

**问题**：无法生成机器码
**解决方案**：

- 确保运行权限足够
- Windows 系统需要管理员权限
- 检查防病毒软件是否阻止

#### 2. 许可证验证失败

**问题**：输入许可证密钥后验证失败
**解决方案**：

- 确认许可证密钥输入正确
- 检查机器码是否匹配
- 联系软件提供商确认许可证状态

#### 3. 硬件变化导致失效

**问题**：更换硬件后许可证失效
**解决方案**：

- 重新获取机器码
- 联系软件提供商重新生成许可证
- 或停用旧许可证，创建新许可证

#### 4. 数据库文件损坏

**问题**：许可证数据库无法访问
**解决方案**：

- 备份现有数据库文件
- 重新运行生成器初始化数据库
- 从备份文件恢复数据

## 📞 技术支持

### 联系方式

- **技术支持邮箱**：<EMAIL>
- **QQ 技术群**：123456789
- **官方网站**：https://example.com

### 日志文件位置

- **生成器日志**：控制台输出
- **客户端日志**：主程序运行日志
- **许可证文件**：`license.json`（客户端）
- **数据库文件**：`license_db.sqlite`（管理端）

## 🔄 版本更新

### v2.0.0 新特性

- ✅ 完整的机器码验证系统
- ✅ 图形化激活界面
- ✅ SQLite 数据库管理
- ✅ 许可证过期检查
- ✅ 多设备支持准备

### 后续计划

- 🔜 在线许可证验证
- 🔜 自动许可证更新
- 🔜 许可证使用统计
- 🔜 批量许可证管理

---

**注意**：本文档面向技术管理员和高级用户。普通用户只需要关注"用户使用指南"部分。
