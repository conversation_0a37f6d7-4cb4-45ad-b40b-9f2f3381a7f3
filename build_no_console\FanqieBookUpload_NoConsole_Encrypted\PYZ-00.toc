('Z:\\Python\\FanqieBookUpload\\build_no_console\\FanqieBookUpload_NoConsole_Encrypted\\PYZ-00.pyz',
 [('OpenSSL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('PySide6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\__init__.py',
   'PYMODULE'),
  ('PySide6.support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\support\\__init__.py',
   'PYMODULE'),
  ('PySide6.support.deprecated',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\PySide6\\support\\deprecated.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_compression.py',
   'PYMODULE'),
  ('_dummy_thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\base64.py',
   'PYMODULE'),
  ('bcrypt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('chardet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.assets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\assets\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\md.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\copy.py',
   'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\decimal.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dis.py',
   'PYMODULE'),
  ('dummy_threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\gettext.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\hmac.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\imp.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ipaddress.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\numbers.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\opcode.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\optparse.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pickle.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\platform.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\py_compile.py',
   'PYMODULE'),
  ('pyarmor_runtime_009284',
   'Z:\\Python\\FanqieBookUpload\\encrypted_temp\\pyarmor_runtime_009284\\__init__.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\random.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\selectors.py',
   'PYMODULE'),
  ('selenium',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\__init__.py',
   'PYMODULE'),
  ('selenium.common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.common.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\common\\exceptions.py',
   'PYMODULE'),
  ('selenium.types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\types.py',
   'PYMODULE'),
  ('selenium.webdriver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\chrome\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.options',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\chrome\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.service',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\chrome\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.webdriver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\chromium\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.options',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\chromium\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.remote_connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\chromium\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.service',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\chromium\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.webdriver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.action_chains',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\action_chains.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.action_builder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\action_builder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.input_device',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\input_device.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.interaction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\interaction.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_actions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_input',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.mouse_button',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\mouse_button.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_actions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_input',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_actions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_input',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.alert',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\alert.py',
   'PYMODULE'),
  ('selenium.webdriver.common.by',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\by.py',
   'PYMODULE'),
  ('selenium.webdriver.common.desired_capabilities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\desired_capabilities.py',
   'PYMODULE'),
  ('selenium.webdriver.common.driver_finder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\driver_finder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.html5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\html5\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.html5.application_cache',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\html5\\application_cache.py',
   'PYMODULE'),
  ('selenium.webdriver.common.keys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\keys.py',
   'PYMODULE'),
  ('selenium.webdriver.common.options',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.print_page_options',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\print_page_options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.proxy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\proxy.py',
   'PYMODULE'),
  ('selenium.webdriver.common.selenium_manager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\selenium_manager.py',
   'PYMODULE'),
  ('selenium.webdriver.common.service',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.common.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\timeouts.py',
   'PYMODULE'),
  ('selenium.webdriver.common.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.common.virtual_authenticator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\common\\virtual_authenticator.py',
   'PYMODULE'),
  ('selenium.webdriver.edge',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\edge\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.options',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\edge\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.service',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\edge\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.webdriver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\edge\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\firefox\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_binary',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_binary.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_profile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_profile.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.options',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\firefox\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.remote_connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\firefox\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.service',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\firefox\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.webdriver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.ie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\ie\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.options',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\ie\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.service',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\ie\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.webdriver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\ie\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\remote\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.bidi_connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\remote\\bidi_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.command',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\remote\\command.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.errorhandler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\remote\\errorhandler.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.file_detector',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\remote\\file_detector.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.mobile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\remote\\mobile.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.remote_connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\remote\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.script_key',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\remote\\script_key.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.shadowroot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\remote\\shadowroot.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.switch_to',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\remote\\switch_to.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\remote\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webdriver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\remote\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webelement',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\remote\\webelement.py',
   'PYMODULE'),
  ('selenium.webdriver.safari',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\safari\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.options',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\safari\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.remote_connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\safari\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.service',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\safari\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.webdriver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\safari\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\support\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.support.expected_conditions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\support\\expected_conditions.py',
   'PYMODULE'),
  ('selenium.webdriver.support.relative_locator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\support\\relative_locator.py',
   'PYMODULE'),
  ('selenium.webdriver.support.select',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\support\\select.py',
   'PYMODULE'),
  ('selenium.webdriver.support.ui',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\support\\ui.py',
   'PYMODULE'),
  ('selenium.webdriver.support.wait',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\support\\wait.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.options',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.service',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.webdriver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.options',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.service',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.webdriver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\webdriver.py',
   'PYMODULE'),
  ('shiboken6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\shiboken6\\__init__.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\signal.py',
   'PYMODULE'),
  ('simplejson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\simplejson\\__init__.py',
   'PYMODULE'),
  ('simplejson.compat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\simplejson\\compat.py',
   'PYMODULE'),
  ('simplejson.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\simplejson\\decoder.py',
   'PYMODULE'),
  ('simplejson.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\simplejson\\encoder.py',
   'PYMODULE'),
  ('simplejson.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\simplejson\\errors.py',
   'PYMODULE'),
  ('simplejson.ordered_dict',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\simplejson\\ordered_dict.py',
   'PYMODULE'),
  ('simplejson.raw_json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\simplejson\\raw_json.py',
   'PYMODULE'),
  ('simplejson.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\simplejson\\scanner.py',
   'PYMODULE'),
  ('six',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\socket.py',
   'PYMODULE'),
  ('socks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('src',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\src\\__init__.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\ssl.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\threading.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.packages',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname._implementation',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\_implementation.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\uu.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\uuid.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\lib\\zipimport.py',
   'PYMODULE')])
