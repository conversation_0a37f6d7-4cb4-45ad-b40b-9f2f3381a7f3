"""
WebDriver管理器 - 自动下载和管理ChromeDriver
"""
import os
import sys
import time
import zipfile
import requests
import platform
from pathlib import Path
from typing import Optional

class WebDriverManager:
    """WebDriver管理器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent.parent
        self.drivers_dir = self.base_dir / "drivers"
        self.drivers_dir.mkdir(exist_ok=True)
        self.system = platform.system().lower()
        
    def get_chrome_version(self) -> Optional[str]:
        """获取Chrome浏览器版本"""
        try:
            if self.system == "windows":
                import winreg
                import subprocess

                # 方法1: 从注册表获取Chrome版本 (HKEY_CURRENT_USER)
                try:
                    key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
                    version = winreg.QueryValueEx(key, "version")[0]
                    winreg.CloseKey(key)
                    if version:
                        print(f"从注册表(HKCU)获取Chrome版本: {version}")
                        return version
                except:
                    pass

                # 方法2: 从注册表获取Chrome版本 (HKEY_LOCAL_MACHINE)
                try:
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Google\Chrome\BLBeacon")
                    version = winreg.QueryValueEx(key, "version")[0]
                    winreg.CloseKey(key)
                    if version:
                        print(f"从注册表(HKLM)获取Chrome版本: {version}")
                        return version
                except:
                    pass

                # 方法3: 从注册表获取Chrome版本 (WOW6432Node)
                try:
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Google\Chrome\BLBeacon")
                    version = winreg.QueryValueEx(key, "version")[0]
                    winreg.CloseKey(key)
                    if version:
                        print(f"从注册表(WOW6432Node)获取Chrome版本: {version}")
                        return version
                except:
                    pass

                # 方法4: 使用Chrome命令行获取版本
                chrome_paths = [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
                ]

                for chrome_path in chrome_paths:
                    if os.path.exists(chrome_path):
                        try:
                            # 设置subprocess参数，隐藏命令行窗口
                            startupinfo = None
                            creationflags = 0
                            if platform.system() == "Windows":
                                if hasattr(subprocess, 'STARTUPINFO'):
                                    startupinfo = subprocess.STARTUPINFO()
                                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                                    startupinfo.wShowWindow = subprocess.SW_HIDE
                                if hasattr(subprocess, 'CREATE_NO_WINDOW'):
                                    creationflags = subprocess.CREATE_NO_WINDOW

                            # 使用chrome --version命令
                            result = subprocess.run([
                                chrome_path, '--version'
                            ], capture_output=True, text=True, timeout=10,
                               startupinfo=startupinfo, creationflags=creationflags)

                            if result.returncode == 0 and result.stdout:
                                # 解析输出，格式通常是 "Google Chrome 139.0.6778.86"
                                output = result.stdout.strip()
                                if 'Chrome' in output:
                                    parts = output.split()
                                    for part in parts:
                                        if '.' in part and part.replace('.', '').isdigit():
                                            print(f"从Chrome命令行获取版本: {part}")
                                            return part
                        except:
                            pass

                        # 方法5: 使用wmic获取文件版本信息
                        try:
                            result = subprocess.run([
                                'wmic', 'datafile', 'where', f'name="{chrome_path.replace(chr(92), chr(92)+chr(92))}"',
                                'get', 'Version', '/value'
                            ], capture_output=True, text=True, timeout=10,
                               startupinfo=startupinfo, creationflags=creationflags)

                            for line in result.stdout.split('\n'):
                                if line.startswith('Version='):
                                    version = line.split('=')[1].strip()
                                    if version:
                                        print(f"从wmic获取Chrome版本: {version}")
                                        return version
                        except:
                            pass

            return None
        except Exception as e:
            print(f"获取Chrome版本时发生错误: {e}")
            return None
    
    def get_chromedriver_version(self, chrome_version: str) -> str:
        """根据Chrome版本获取匹配的ChromeDriver版本"""
        try:
            major_version = chrome_version.split('.')[0]
            print(f"Chrome主版本号: {major_version}")

            # Chrome 115+使用新的下载地址和API
            if int(major_version) >= 115:
                # 方法1: 尝试获取指定主版本的最新版本
                try:
                    url = f"https://googlechromelabs.github.io/chrome-for-testing/latest-versions-per-milestone-with-downloads.json"
                    print(f"正在从新API获取ChromeDriver版本信息...")
                    response = requests.get(url, timeout=15)
                    response.raise_for_status()
                    data = response.json()

                    if major_version in data['milestones']:
                        milestone_data = data['milestones'][major_version]
                        version = milestone_data['version']
                        print(f"找到匹配的ChromeDriver版本: {version}")

                        # 验证下载链接是否存在
                        if 'downloads' in milestone_data and 'chromedriver' in milestone_data['downloads']:
                            downloads = milestone_data['downloads']['chromedriver']
                            for download in downloads:
                                if 'win32' in download['platform'] or 'win64' in download['platform']:
                                    print(f"确认下载链接可用: {download['url']}")
                                    return version
                    else:
                        print(f"未找到主版本 {major_version} 的ChromeDriver")
                except Exception as e:
                    print(f"从新API获取版本失败: {e}")

                # 方法2: 尝试获取最新稳定版本
                try:
                    url = "https://googlechromelabs.github.io/chrome-for-testing/last-known-good-versions-with-downloads.json"
                    print(f"尝试获取最新稳定版本...")
                    response = requests.get(url, timeout=15)
                    response.raise_for_status()
                    data = response.json()

                    if 'channels' in data and 'Stable' in data['channels']:
                        stable_data = data['channels']['Stable']
                        version = stable_data['version']
                        stable_major = version.split('.')[0]

                        # 如果稳定版本的主版本号匹配或接近，使用它
                        if abs(int(stable_major) - int(major_version)) <= 1:
                            print(f"使用稳定版本: {version}")
                            return version
                except Exception as e:
                    print(f"获取稳定版本失败: {e}")

                # 方法3: 使用已知的版本映射（基于Chrome for Testing最新稳定版本）
                version_mapping = {
                    "139": "139.0.7258.66",  # 更新为最新稳定版本
                    "138": "138.0.6961.69",
                    "137": "137.0.6864.75",
                    "136": "136.0.6776.72",
                    "135": "135.0.6790.75"
                }

                if major_version in version_mapping:
                    fallback_version = version_mapping[major_version]
                    print(f"使用预设版本映射: {fallback_version}")
                    return fallback_version

            # Chrome 114及以下版本使用旧API
            else:
                url = f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}"
                print(f"使用旧API获取ChromeDriver版本...")
                response = requests.get(url, timeout=10)
                response.raise_for_status()
                version = response.text.strip()
                print(f"获取到版本: {version}")
                return version

        except Exception as e:
            print(f"获取ChromeDriver版本失败: {e}")

        # 最终回退版本
        fallback_version = "119.0.6045.105"
        print(f"使用回退版本: {fallback_version}")
        return fallback_version
    
    def download_chromedriver(self, version: str) -> Optional[str]:
        """下载ChromeDriver"""
        try:
            major_version = int(version.split('.')[0])

            # 确定平台名称
            if self.system == "windows":
                if platform.machine().endswith('64'):
                    platform_name = "win64"
                else:
                    platform_name = "win32"
            else:
                platform_name = "win32"

            print(f"目标平台: {platform_name}")

            # 确定下载URL
            download_urls = []

            if major_version >= 115:
                # 新版本下载地址 - 尝试多个镜像
                base_urls = [
                    f"https://edgedl.me.gvt1.com/edgedl/chrome/chrome-for-testing/{version}/{platform_name}/chromedriver-{platform_name}.zip",
                    f"https://storage.googleapis.com/chrome-for-testing-public/{version}/{platform_name}/chromedriver-{platform_name}.zip"
                ]
                download_urls.extend(base_urls)
            else:
                # 旧版本下载地址
                download_urls.append(f"https://chromedriver.storage.googleapis.com/{version}/chromedriver_win32.zip")

            # 尝试下载
            for i, url in enumerate(download_urls):
                try:
                    print(f"尝试下载链接 {i+1}/{len(download_urls)}: {url}")

                    # 设置请求头，模拟浏览器
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                    }

                    response = requests.get(url, timeout=60, headers=headers, stream=True)
                    response.raise_for_status()

                    # 检查内容类型
                    content_type = response.headers.get('content-type', '')
                    if 'zip' not in content_type and 'application/octet-stream' not in content_type:
                        print(f"警告: 响应内容类型不是zip文件: {content_type}")

                    # 保存zip文件
                    zip_path = self.drivers_dir / f"chromedriver_{version}_{platform_name}.zip"

                    print(f"正在下载到: {zip_path}")
                    total_size = int(response.headers.get('content-length', 0))
                    downloaded = 0

                    with open(zip_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                downloaded += len(chunk)
                                if total_size > 0:
                                    progress = (downloaded / total_size) * 100
                                    print(f"\r下载进度: {progress:.1f}%", end='', flush=True)

                    print(f"\n下载完成，文件大小: {downloaded} 字节")

                    # 验证文件大小
                    if downloaded < 1000:  # 小于1KB可能是错误页面
                        print("下载的文件太小，可能是错误响应")
                        zip_path.unlink()
                        continue

                    # 解压文件
                    print("正在解压文件...")
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        zip_ref.extractall(self.drivers_dir)

                    # 删除zip文件
                    zip_path.unlink()

                    # 找到chromedriver可执行文件
                    chromedriver_path = None
                    for root, dirs, files in os.walk(self.drivers_dir):
                        for file in files:
                            if file.startswith('chromedriver') and (file.endswith('.exe') or ('.' not in file and 'chromedriver' == file)):
                                full_path = os.path.join(root, file)
                                # 排除已存在的目标文件
                                target_name = "chromedriver.exe" if self.system == "windows" else "chromedriver"
                                if not full_path.endswith(target_name):
                                    chromedriver_path = full_path
                                    break
                        if chromedriver_path:
                            break

                    if chromedriver_path:
                        # 移动到drivers目录根目录
                        final_path = self.drivers_dir / ("chromedriver.exe" if self.system == "windows" else "chromedriver")

                        # 备份现有文件
                        if final_path.exists():
                            backup_path = self.drivers_dir / f"chromedriver_backup_{int(time.time())}.exe"
                            os.rename(final_path, backup_path)
                            print(f"已备份旧版本到: {backup_path}")

                        os.rename(chromedriver_path, final_path)

                        # 给可执行权限（非Windows系统）
                        if self.system != "windows":
                            os.chmod(final_path, 0o755)

                        print(f"ChromeDriver 下载并安装成功: {final_path}")
                        return str(final_path)
                    else:
                        print("解压后未找到chromedriver可执行文件")
                        continue

                except requests.exceptions.RequestException as e:
                    print(f"下载失败: {e}")
                    continue
                except zipfile.BadZipFile as e:
                    print(f"zip文件损坏: {e}")
                    if 'zip_path' in locals() and zip_path.exists():
                        zip_path.unlink()
                    continue
                except Exception as e:
                    print(f"处理下载文件时出错: {e}")
                    continue

            print("所有下载链接都失败了")
            return None

        except Exception as e:
            print(f"下载 ChromeDriver 时发生未预期的错误: {e}")
            return None
    
    def check_chromedriver_compatibility(self, chromedriver_path: str, chrome_version: str) -> bool:
        """检查ChromeDriver与Chrome版本的兼容性"""
        try:
            import subprocess

            # 设置subprocess参数，隐藏命令行窗口
            startupinfo = None
            creationflags = 0
            if platform.system() == "Windows":
                if hasattr(subprocess, 'STARTUPINFO'):
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = subprocess.SW_HIDE
                if hasattr(subprocess, 'CREATE_NO_WINDOW'):
                    creationflags = subprocess.CREATE_NO_WINDOW

            # 获取ChromeDriver版本
            result = subprocess.run([
                chromedriver_path, '--version'
            ], capture_output=True, text=True, timeout=10,
               startupinfo=startupinfo, creationflags=creationflags)

            if result.returncode == 0 and result.stdout:
                driver_output = result.stdout.strip()
                print(f"ChromeDriver版本信息: {driver_output}")

                # 解析版本号
                for part in driver_output.split():
                    if '.' in part and part.replace('.', '').isdigit():
                        driver_version = part
                        chrome_major = chrome_version.split('.')[0]
                        driver_major = driver_version.split('.')[0]

                        # 检查主版本号是否匹配
                        if chrome_major == driver_major:
                            print(f"版本兼容: Chrome {chrome_major} <-> ChromeDriver {driver_major}")
                            return True
                        else:
                            print(f"版本不兼容: Chrome {chrome_major} <-> ChromeDriver {driver_major}")
                            return False

            return False
        except Exception as e:
            print(f"检查ChromeDriver兼容性时出错: {e}")
            return False

    def get_chromedriver_path(self, force_update: bool = False) -> Optional[str]:
        """获取或下载ChromeDriver路径"""
        try:
            # 获取Chrome版本
            chrome_version = self.get_chrome_version()
            if not chrome_version:
                print("无法检测到Chrome浏览器版本，请确保已安装Chrome浏览器")
                return None

            print(f"检测到Chrome版本: {chrome_version}")
            chrome_major = chrome_version.split('.')[0]

            # 检查本地是否已有chromedriver
            chromedriver_name = "chromedriver.exe" if self.system == "windows" else "chromedriver"
            local_path = self.drivers_dir / chromedriver_name

            # 如果存在本地文件且不强制更新，检查兼容性
            if local_path.exists() and not force_update:
                print(f"发现本地ChromeDriver: {local_path}")

                # 检查版本兼容性
                if self.check_chromedriver_compatibility(str(local_path), chrome_version):
                    print("本地ChromeDriver版本兼容，直接使用")
                    return str(local_path)
                else:
                    print("本地ChromeDriver版本不兼容，需要更新")
                    force_update = True

            # 如果需要下载或更新
            if not local_path.exists() or force_update:
                print("需要下载新的ChromeDriver")

                # 获取匹配的ChromeDriver版本
                driver_version = self.get_chromedriver_version(chrome_version)
                print(f"目标ChromeDriver版本: {driver_version}")

                # 下载ChromeDriver
                downloaded_path = self.download_chromedriver(driver_version)
                if downloaded_path:
                    print(f"ChromeDriver下载成功: {downloaded_path}")
                    return downloaded_path
                else:
                    print("ChromeDriver下载失败")
                    # 如果下载失败但本地有文件，尝试使用本地文件
                    if local_path.exists():
                        print("下载失败，尝试使用本地现有版本")
                        return str(local_path)
                    return None

            return str(local_path) if local_path.exists() else None

        except Exception as e:
            print(f"获取ChromeDriver失败: {e}")
            # 如果出错但本地有文件，尝试使用本地文件
            chromedriver_name = "chromedriver.exe" if self.system == "windows" else "chromedriver"
            local_path = self.drivers_dir / chromedriver_name
            if local_path.exists():
                print("出错时使用本地现有版本")
                return str(local_path)
            return None
