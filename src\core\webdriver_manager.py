"""
WebDriver管理器 - 自动下载和管理ChromeDriver
"""
import os
import sys
import zipfile
import requests
import platform
from pathlib import Path
from typing import Optional

class WebDriverManager:
    """WebDriver管理器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent.parent
        self.drivers_dir = self.base_dir / "drivers"
        self.drivers_dir.mkdir(exist_ok=True)
        self.system = platform.system().lower()
        
    def get_chrome_version(self) -> Optional[str]:
        """获取Chrome浏览器版本"""
        try:
            if self.system == "windows":
                import winreg
                try:
                    # 尝试从注册表获取Chrome版本
                    key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
                    version = winreg.QueryValueEx(key, "version")[0]
                    winreg.CloseKey(key)
                    return version
                except:
                    # 尝试从程序文件夹获取
                    chrome_paths = [
                        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                        os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
                    ]
                    
                    for chrome_path in chrome_paths:
                        if os.path.exists(chrome_path):
                            # 使用wmic获取版本信息
                            import subprocess
                            try:
                                # 设置subprocess参数，隐藏命令行窗口
                                startupinfo = None
                                creationflags = 0
                                if platform.system() == "Windows":
                                    if hasattr(subprocess, 'STARTUPINFO'):
                                        startupinfo = subprocess.STARTUPINFO()
                                        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                                        startupinfo.wShowWindow = subprocess.SW_HIDE
                                    if hasattr(subprocess, 'CREATE_NO_WINDOW'):
                                        creationflags = subprocess.CREATE_NO_WINDOW

                                result = subprocess.run([
                                    'wmic', 'datafile', 'where', f'name="{chrome_path.replace(chr(92), chr(92)+chr(92))}"',
                                    'get', 'Version', '/value'
                                ], capture_output=True, text=True, timeout=10,
                                   startupinfo=startupinfo, creationflags=creationflags)

                                for line in result.stdout.split('\n'):
                                    if line.startswith('Version='):
                                        return line.split('=')[1].strip()
                            except:
                                pass
                    
            return None
        except Exception:
            return None
    
    def get_chromedriver_version(self, chrome_version: str) -> str:
        """根据Chrome版本获取匹配的ChromeDriver版本"""
        try:
            major_version = chrome_version.split('.')[0]
            
            # Chrome 115+使用新的下载地址
            if int(major_version) >= 115:
                url = f"https://googlechromelabs.github.io/chrome-for-testing/latest-versions-per-milestone-with-downloads.json"
                response = requests.get(url, timeout=10)
                data = response.json()
                
                if major_version in data['milestones']:
                    downloads = data['milestones'][major_version]['downloads']['chromedriver']
                    for download in downloads:
                        if 'win32' in download['platform'] or 'win64' in download['platform']:
                            return data['milestones'][major_version]['version']
            
            # Chrome 114及以下版本
            url = f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}"
            response = requests.get(url, timeout=10)
            return response.text.strip()
            
        except Exception as e:
            print(f"获取ChromeDriver版本失败: {e}")
            # 返回一个通用版本
            return "119.0.6045.105"
    
    def download_chromedriver(self, version: str) -> Optional[str]:
        """下载ChromeDriver"""
        try:
            # 确定下载URL和文件名
            if int(version.split('.')[0]) >= 115:
                # 新版本下载地址
                if self.system == "windows":
                    if platform.machine().endswith('64'):
                        platform_name = "win64"
                    else:
                        platform_name = "win32"
                else:
                    platform_name = "win32"
                
                url = f"https://edgedl.me.gvt1.com/edgedl/chrome/chrome-for-testing/{version}/{platform_name}/chromedriver-{platform_name}.zip"
            else:
                # 旧版本下载地址
                if self.system == "windows":
                    url = f"https://chromedriver.storage.googleapis.com/{version}/chromedriver_win32.zip"
                else:
                    url = f"https://chromedriver.storage.googleapis.com/{version}/chromedriver_win32.zip"
            
            # 下载文件
            print(f"正在下载 ChromeDriver {version}...")
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            # 保存zip文件
            zip_path = self.drivers_dir / f"chromedriver_{version}.zip"
            with open(zip_path, 'wb') as f:
                f.write(response.content)
            
            # 解压文件
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.drivers_dir)
            
            # 删除zip文件
            zip_path.unlink()
            
            # 找到chromedriver可执行文件
            chromedriver_path = None
            for root, dirs, files in os.walk(self.drivers_dir):
                for file in files:
                    if file.startswith('chromedriver') and (file.endswith('.exe') or '.' not in file):
                        chromedriver_path = os.path.join(root, file)
                        break
                if chromedriver_path:
                    break
            
            if chromedriver_path:
                # 移动到drivers目录根目录
                final_path = self.drivers_dir / ("chromedriver.exe" if self.system == "windows" else "chromedriver")
                if os.path.exists(final_path):
                    os.remove(final_path)
                
                os.rename(chromedriver_path, final_path)
                
                # 给可执行权限（非Windows系统）
                if self.system != "windows":
                    os.chmod(final_path, 0o755)
                
                print(f"ChromeDriver 下载成功: {final_path}")
                return str(final_path)
            
            return None
            
        except Exception as e:
            print(f"下载 ChromeDriver 失败: {e}")
            return None
    
    def get_chromedriver_path(self) -> Optional[str]:
        """获取或下载ChromeDriver路径"""
        try:
            # 检查本地是否已有chromedriver
            chromedriver_name = "chromedriver.exe" if self.system == "windows" else "chromedriver"
            local_path = self.drivers_dir / chromedriver_name
            
            if local_path.exists():
                return str(local_path)
            
            # 获取Chrome版本
            chrome_version = self.get_chrome_version()
            if not chrome_version:
                print("无法检测到Chrome浏览器版本，请确保已安装Chrome浏览器")
                return None
            
            print(f"检测到Chrome版本: {chrome_version}")
            
            # 获取匹配的ChromeDriver版本
            driver_version = self.get_chromedriver_version(chrome_version)
            print(f"需要ChromeDriver版本: {driver_version}")
            
            # 下载ChromeDriver
            return self.download_chromedriver(driver_version)
            
        except Exception as e:
            print(f"获取ChromeDriver失败: {e}")
            return None
