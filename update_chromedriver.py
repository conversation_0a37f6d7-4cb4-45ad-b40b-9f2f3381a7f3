#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ChromeDriver更新脚本
强制下载与当前Chrome版本匹配的ChromeDriver
"""

import os
import sys
import time
import zipfile
import requests
import platform
from pathlib import Path
from typing import Optional

def download_chromedriver(version: str, drivers_dir: Path) -> Optional[str]:
    """下载指定版本的ChromeDriver"""
    try:
        system = platform.system().lower()
        
        # 确定平台名称
        if system == "windows":
            if platform.machine().endswith('64'):
                platform_name = "win64"
            else:
                platform_name = "win32"
        else:
            platform_name = "win32"
        
        print(f"目标平台: {platform_name}")
        
        # 确定下载URL
        major_version = int(version.split('.')[0])
        download_urls = []
        
        if major_version >= 115:
            # 新版本下载地址 - 尝试多个镜像
            base_urls = [
                f"https://storage.googleapis.com/chrome-for-testing-public/{version}/{platform_name}/chromedriver-{platform_name}.zip",
                f"https://edgedl.me.gvt1.com/edgedl/chrome/chrome-for-testing/{version}/{platform_name}/chromedriver-{platform_name}.zip"
            ]
            download_urls.extend(base_urls)
        else:
            # 旧版本下载地址
            download_urls.append(f"https://chromedriver.storage.googleapis.com/{version}/chromedriver_win32.zip")
        
        # 尝试下载
        for i, url in enumerate(download_urls):
            try:
                print(f"尝试下载链接 {i+1}/{len(download_urls)}: {url}")
                
                # 设置请求头，模拟浏览器
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                }
                
                response = requests.get(url, timeout=60, headers=headers, stream=True)
                response.raise_for_status()
                
                # 检查内容类型
                content_type = response.headers.get('content-type', '')
                print(f"响应内容类型: {content_type}")
                
                # 保存zip文件
                zip_path = drivers_dir / f"chromedriver_{version}_{platform_name}.zip"
                
                print(f"正在下载到: {zip_path}")
                total_size = int(response.headers.get('content-length', 0))
                downloaded = 0
                
                with open(zip_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)
                            if total_size > 0:
                                progress = (downloaded / total_size) * 100
                                print(f"\r下载进度: {progress:.1f}%", end='', flush=True)
                
                print(f"\n下载完成，文件大小: {downloaded} 字节")
                
                # 验证文件大小
                if downloaded < 1000:  # 小于1KB可能是错误页面
                    print("下载的文件太小，可能是错误响应")
                    zip_path.unlink()
                    continue
                
                # 解压文件
                print("正在解压文件...")
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(drivers_dir)
                
                # 删除zip文件
                zip_path.unlink()
                
                # 找到chromedriver可执行文件
                chromedriver_path = None
                for root, dirs, files in os.walk(drivers_dir):
                    for file in files:
                        if file.startswith('chromedriver') and (file.endswith('.exe') or ('.' not in file and 'chromedriver' == file)):
                            full_path = os.path.join(root, file)
                            # 排除已存在的目标文件
                            target_name = "chromedriver.exe" if system == "windows" else "chromedriver"
                            if not full_path.endswith(target_name):
                                chromedriver_path = full_path
                                break
                    if chromedriver_path:
                        break
                
                if chromedriver_path:
                    # 移动到drivers目录根目录
                    final_path = drivers_dir / ("chromedriver.exe" if system == "windows" else "chromedriver")
                    
                    # 备份现有文件
                    if final_path.exists():
                        backup_path = drivers_dir / f"chromedriver_backup_{int(time.time())}.exe"
                        os.rename(final_path, backup_path)
                        print(f"已备份旧版本到: {backup_path}")
                    
                    os.rename(chromedriver_path, final_path)
                    
                    # 给可执行权限（非Windows系统）
                    if system != "windows":
                        os.chmod(final_path, 0o755)
                    
                    print(f"ChromeDriver 下载并安装成功: {final_path}")
                    return str(final_path)
                else:
                    print("解压后未找到chromedriver可执行文件")
                    continue
                    
            except requests.exceptions.RequestException as e:
                print(f"下载失败: {e}")
                continue
            except zipfile.BadZipFile as e:
                print(f"zip文件损坏: {e}")
                if 'zip_path' in locals() and zip_path.exists():
                    zip_path.unlink()
                continue
            except Exception as e:
                print(f"处理下载文件时出错: {e}")
                continue
        
        print("所有下载链接都失败了")
        return None
        
    except Exception as e:
        print(f"下载 ChromeDriver 时发生未预期的错误: {e}")
        return None

def get_chrome_version() -> Optional[str]:
    """获取Chrome浏览器版本"""
    try:
        system = platform.system().lower()
        if system == "windows":
            import winreg
            import subprocess
            
            # 从注册表获取Chrome版本
            try:
                key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
                version = winreg.QueryValueEx(key, "version")[0]
                winreg.CloseKey(key)
                if version:
                    return version
            except:
                pass
            
            # 使用Chrome命令行获取版本
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
            ]
            
            for chrome_path in chrome_paths:
                if os.path.exists(chrome_path):
                    try:
                        result = subprocess.run([
                            chrome_path, '--version'
                        ], capture_output=True, text=True, timeout=10)
                        
                        if result.returncode == 0 and result.stdout:
                            output = result.stdout.strip()
                            if 'Chrome' in output:
                                parts = output.split()
                                for part in parts:
                                    if '.' in part and part.replace('.', '').replace('-', '').isdigit():
                                        return part
                    except:
                        pass
                    
        return None
    except Exception:
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("ChromeDriver自动更新工具")
    print("=" * 60)
    
    # 设置目录
    base_dir = Path(__file__).parent
    drivers_dir = base_dir / "drivers"
    drivers_dir.mkdir(exist_ok=True)
    
    # 获取Chrome版本
    print("1. 检测Chrome版本...")
    chrome_version = get_chrome_version()
    
    if not chrome_version:
        print("✗ 无法检测到Chrome版本，请确保已安装Chrome浏览器")
        return False
    
    print(f"✓ 检测到Chrome版本: {chrome_version}")
    major_version = chrome_version.split('.')[0]
    
    # 确定需要的ChromeDriver版本
    print("2. 确定ChromeDriver版本...")
    
    # 版本映射表（基于Chrome for Testing最新稳定版本）
    version_mapping = {
        "139": "139.0.7258.66",  # 更新为最新稳定版本
        "138": "138.0.6961.69",
        "137": "137.0.6864.75",
        "136": "136.0.6776.72",
        "135": "135.0.6790.75"
    }
    
    if major_version in version_mapping:
        driver_version = version_mapping[major_version]
    else:
        # 尝试使用Chrome版本作为ChromeDriver版本
        driver_version = chrome_version
    
    print(f"✓ 目标ChromeDriver版本: {driver_version}")
    
    # 检查现有版本
    print("3. 检查现有ChromeDriver...")
    chromedriver_name = "chromedriver.exe" if platform.system().lower() == "windows" else "chromedriver"
    local_path = drivers_dir / chromedriver_name
    
    if local_path.exists():
        print(f"发现现有ChromeDriver: {local_path}")
        
        # 获取现有版本
        try:
            import subprocess
            result = subprocess.run([
                str(local_path), '--version'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout:
                print(f"现有版本: {result.stdout.strip()}")
        except:
            pass
    
    # 下载新版本
    print("4. 下载新的ChromeDriver...")
    downloaded_path = download_chromedriver(driver_version, drivers_dir)
    
    if downloaded_path:
        print(f"\n🎉 ChromeDriver更新成功！")
        print(f"Chrome版本: {chrome_version}")
        print(f"ChromeDriver版本: {driver_version}")
        print(f"安装路径: {downloaded_path}")
        
        # 验证新版本
        try:
            import subprocess
            result = subprocess.run([
                downloaded_path, '--version'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout:
                print(f"验证成功: {result.stdout.strip()}")
        except Exception as e:
            print(f"验证时出错: {e}")
        
        return True
    else:
        print("\n✗ ChromeDriver更新失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        print("\n" + "=" * 60)
        if success:
            print("更新完成！现在可以正常使用Chrome浏览器自动化功能。")
        else:
            print("更新失败！请检查网络连接或手动下载ChromeDriver。")
        print("=" * 60)
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n更新被用户中断")
    except Exception as e:
        print(f"\n更新过程中发生错误: {e}")
        input("\n按回车键退出...")
