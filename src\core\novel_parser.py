"""
小说文本解析器
"""
import re
from typing import List, Dict, Tuple, Optional

class NovelParser:
    """小说文本解析器"""
    
    def __init__(self):
        # 章节标题匹配模式：#**章节标题**#
        self.chapter_pattern = re.compile(r'#\*\*(.*?)\*\*')
        
        # 章节数字匹配模式（支持中文数字和阿拉伯数字）
        self.chapter_number_patterns = [
            re.compile(r'第(\d+)章'),  # 第1章, 第2章
            re.compile(r'第([一二三四五六七八九十百千万]+)章'),  # 第一章, 第二章
            re.compile(r'Chapter\s*(\d+)', re.IGNORECASE),  # Chapter 1
            re.compile(r'(\d+)\.'),  # 1. 2.
            re.compile(r'(\d+)'),  # 纯数字
        ]
        
        # 中文数字转换字典
        self.chinese_numbers = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
            '十一': 11, '十二': 12, '十三': 13, '十四': 14, '十五': 15,
            '十六': 16, '十七': 17, '十八': 18, '十九': 19, '二十': 20,
            '二十一': 21, '二十二': 22, '二十三': 23, '二十四': 24, '二十五': 25,
            '二十六': 26, '二十七': 27, '二十八': 28, '二十九': 29, '三十': 30
        }
    
    def extract_chapter_info(self, title: str) -> Tuple[Optional[int], str]:
        """
        提取章节数字和章节名称
        
        Args:
            title: 原始章节标题，例如："第二章 觉醒"
            
        Returns:
            Tuple[Optional[int], str]: (章节数字, 章节名称)
        """
        original_title = title.strip()
        chapter_number = None
        chapter_name = original_title
        
        # 尝试各种章节数字匹配模式
        for pattern in self.chapter_number_patterns:
            match = pattern.search(title)
            if match:
                number_str = match.group(1)
                
                # 尝试转换为数字
                try:
                    # 如果是纯数字
                    if number_str.isdigit():
                        chapter_number = int(number_str)
                    # 如果是中文数字
                    elif number_str in self.chinese_numbers:
                        chapter_number = self.chinese_numbers[number_str]
                    # 复杂中文数字（如二十一）
                    else:
                        chapter_number = self._parse_chinese_number(number_str)
                    
                    # 提取章节名称（去掉章节数字部分）
                    if chapter_number is not None:
                        # 移除匹配到的部分，获取章节名称
                        chapter_name = pattern.sub('', title).strip()
                        
                        # 进一步清理章节名称
                        chapter_name = re.sub(r'^[:\s\-—–_]*', '', chapter_name)  # 去掉开头的分隔符
                        chapter_name = re.sub(r'[:\s\-—–_]*$', '', chapter_name)  # 去掉结尾的分隔符
                        
                        # 如果章节名称为空，使用默认名称
                        if not chapter_name:
                            chapter_name = f"第{chapter_number}章"
                        
                        break
                        
                except (ValueError, TypeError):
                    continue
        
        # 如果没有找到章节数字，章节名称就是整个标题
        if chapter_number is None:
            chapter_name = original_title
        
        return chapter_number, chapter_name
    
    def _parse_chinese_number(self, chinese_str: str) -> Optional[int]:
        """
        解析复杂的中文数字
        
        Args:
            chinese_str: 中文数字字符串
            
        Returns:
            Optional[int]: 转换后的数字，失败返回None
        """
        try:
            # 直接查找预定义的中文数字
            if chinese_str in self.chinese_numbers:
                return self.chinese_numbers[chinese_str]
            
            # 简单的二十+个位数解析
            if chinese_str.startswith('二十'):
                remainder = chinese_str[2:]
                if remainder in self.chinese_numbers:
                    return 20 + self.chinese_numbers[remainder]
                elif not remainder:
                    return 20
            
            # 三十+个位数解析
            elif chinese_str.startswith('三十'):
                remainder = chinese_str[2:]
                if remainder in self.chinese_numbers:
                    return 30 + self.chinese_numbers[remainder]
                elif not remainder:
                    return 30
            
            # 可以继续扩展其他数字解析规则
            
        except (KeyError, IndexError):
            pass
        
        return None
    
    def process_chapter_content(self, content: str) -> str:
        """
        处理章节内容，保持原始格式，只去除空行
        
        Args:
            content: 原始章节内容
            
        Returns:
            str: 处理后的章节内容（去除空行但保持其他格式）
        """
        if not content:
            return content
        
        # 1. 统一换行符为 \n
        content = content.replace('\r\n', '\n').replace('\r', '\n')
        
        # 2. 分割成行
        lines = content.split('\n')
        
        # 3. 去除空行，但保持每行的原始格式（包括缩进和空格）
        non_empty_lines = []
        for line in lines:
            # 只去除完全为空的行，保留只有空格或制表符的行（可能有格式意义）
            if line.strip():  # 如果去除首尾空白后不为空，则保留
                non_empty_lines.append(line)
        
        # 4. 重新组合，去除开头和结尾的多余空白
        result = '\n'.join(non_empty_lines).strip()
        
        return result
    
    
    def parse_novel(self, content: str) -> List[Dict[str, str]]:
        """
        解析小说内容
        
        Args:
            content: 小说文本内容
            
        Returns:
            List[Dict]: 章节列表，每个元素包含详细信息
        """
        chapters = []
        
        # 分割章节
        parts = self.chapter_pattern.split(content)
        
        # 第一部分通常是标题前的内容，可能为空
        if len(parts) > 1:
            for i in range(1, len(parts), 2):
                if i + 1 < len(parts):
                    title = parts[i].strip()
                    chapter_content = parts[i + 1].strip()
                    
                    if title and chapter_content:
                        # 优化章节内容的换行处理
                        processed_content = self.process_chapter_content(chapter_content)
                        
                        # 提取章节数字和名称
                        chapter_number, chapter_name = self.extract_chapter_info(title)
                        
                        chapters.append({
                            'original_title': title,  # 原始标题
                            'title': title,  # 保持原始标题用于显示
                            'chapter_number': chapter_number,  # 章节数字
                            'chapter_name': chapter_name,  # 章节名称
                            'content': processed_content,
                            'word_count': len(processed_content)
                        })
        
        return chapters
    
    def validate_format(self, content: str) -> Tuple[bool, str]:
        """
        验证小说格式是否正确
        
        Args:
            content: 小说文本内容
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if not content.strip():
            return False, "文件内容为空"
        
        # 检查是否包含章节标题格式
        matches = self.chapter_pattern.findall(content)
        if not matches:
            return False, "未找到符合格式的章节标题（#**章节标题**）"
        
        # 检查每个章节是否有内容
        chapters = self.parse_novel(content)
        if not chapters:
            return False, "未找到有效的章节内容"
        
        return True, f"成功解析到 {len(chapters)} 个章节"
    
    def get_novel_info(self, content: str) -> Dict:
        """
        获取小说基本信息
        
        Args:
            content: 小说文本内容
            
        Returns:
            Dict: 小说信息
        """
        chapters = self.parse_novel(content)
        total_words = sum(chapter['word_count'] for chapter in chapters)
        
        return {
            'total_chapters': len(chapters),
            'total_words': total_words,
            'chapters': chapters
        }
