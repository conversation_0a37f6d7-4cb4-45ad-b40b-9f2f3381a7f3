<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="238" name="Python" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="HttpUrlsUsage" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredUrls">
        <list>
          <option value="http://localhost" />
          <option value="http://127.0.0.1" />
          <option value="http://www.w3.org/2000/svg" />
          <option value="http://www.w3.org/2000/10/XMLSchema" />
          <option value="http://www.w3.org/2001/XMLSchema" />
          <option value="http://www.w3.org/2001/XMLSchema-instance" />
          <option value="http://www.w3.org/2003/03/wsdl" />
          <option value="http://json-schema.org/draft" />
          <option value="http://java.sun.com/xml/ns/" />
          <option value="http://xmlns.jcp.org/xml/ns/" />
          <option value="http://javafx.com/javafx/" />
          <option value="http://javafx.com/fxml" />
          <option value="http://maven.apache.org/xsd/" />
          <option value="http://maven.apache.org/POM/" />
          <option value="http://www.springframework.org/schema/" />
          <option value="http://www.thymeleaf.org" />
          <option value="http://www.ibm.com/webservices/xsd" />
          <option value="http://schemas.xmlsoap.org/" />
          <option value="http://cxf.apache.org/schemas/" />
          <option value="http://yy.test.meidan.vip" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8Inspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="E501" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N806" />
          <option value="N803" />
          <option value="N802" />
          <option value="N801" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyShadowingBuiltinsInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredNames">
        <list>
          <option value="id" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="str.keyword" />
          <option value="str.result" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>