#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
许可证管理系统 - PyArmor加密打包脚本

此脚本执行以下操作：
1. 使用PyArmor加密项目代码
2. 使用PyInstaller打包加密后的代码为可执行文件
3. 针对许可证管理系统进行了专门优化
4. 优化版本：彻底解决终端窗口弹出问题
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
from datetime import datetime

# 项目根目录
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))

# 输出目录
BUILD_DIR = os.path.join(ROOT_DIR, "build_encrypted")
DIST_DIR = os.path.join(ROOT_DIR, "dist_encrypted")

# 需要加密的Python文件
MODULES_TO_ENCRYPT = [
    "machine_code_generator.py",
    "ultimate_no_console.py",
    "create_test_license.py",
    "demo_license_system.py"
]

# 需要复制的资源文件
RESOURCES_TO_COPY = [
    "机器码验证系统使用说明.md",
    "license_db.sqlite",
]

# 可选资源文件（存在则复制）
OPTIONAL_RESOURCES = [
    "logo.ico",
    "config.ini",
    "settings.json",
]

# PyInstaller 规格文件内容模板
SPEC_TEMPLATE = '''# -*- mode: python ; coding: utf-8 -*-

import os
import sys

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(SPEC))
sys.path.insert(0, current_dir)

block_cipher = None

# 主程序入口
main_script = os.path.join(current_dir, 'ultimate_no_console.py')

# 数据文件
datas = []

# 添加数据库文件
db_file = os.path.join(current_dir, 'license_db.sqlite')
if os.path.exists(db_file):
    datas.append((db_file, '.'))

# 添加说明文档
readme_file = os.path.join(current_dir, '机器码验证系统使用说明.md')
if os.path.exists(readme_file):
    datas.append((readme_file, '.'))

# 添加图标文件
icon_file = os.path.join(current_dir, 'logo.ico')
if os.path.exists(icon_file):
    datas.append((icon_file, '.'))

# 隐藏导入
hiddenimports = [
    # Python标准库
    'json',
    'sqlite3',
    'hashlib',
    'hmac',
    'base64',
    'platform',
    'uuid',
    'subprocess',
    'datetime',
    'pathlib',
    'os',
    'sys',
    'shutil',
    'threading',
    # PySide6模块
    'PySide6.QtCore',
    'PySide6.QtGui',
    'PySide6.QtWidgets',
    # 加密后的模块
    'machine_code_generator',
    'create_test_license',
    'demo_license_system',
]

a = Analysis(
    [main_script],
    pathex=[current_dir],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='许可证管理器',
    debug=False,
    bootloader_ignore_signals=True,  # 忽略引导程序信号
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # GUI应用，完全禁用控制台
    disable_windowed_traceback=True,  # 禁用窗口化回溯
    argv_emulation=False,  # 禁用参数模拟
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_file if os.path.exists(icon_file) else None,
)
'''


def print_banner():
    """打印横幅"""
    print("=" * 80)
    print("       许可证管理系统 - 加密打包工具 v1.1 (终端窗口优化版)")
    print("=" * 80)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"项目目录: {ROOT_DIR}")
    print("=" * 80)


def check_dependencies():
    """检查依赖工具"""
    print("\n🔍 检查依赖工具...")
    
    # 检查PyArmor
    try:
        result = subprocess.run(['pyarmor', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ PyArmor: {result.stdout.strip()}")
        else:
            print("❌ PyArmor 未安装或不可用")
            print("   请运行: pip install pyarmor")
            return False
    except FileNotFoundError:
        print("❌ PyArmor 未安装")
        print("   请运行: pip install pyarmor")
        return False
    
    # 检查PyInstaller
    try:
        result = subprocess.run(['pyinstaller', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ PyInstaller: {result.stdout.strip()}")
        else:
            print("❌ PyInstaller 未安装或不可用")
            print("   请运行: pip install pyinstaller")
            return False
    except FileNotFoundError:
        print("❌ PyInstaller 未安装")
        print("   请运行: pip install pyinstaller")
        return False
    
    return True


def clean_directories():
    """清理构建和分发目录"""
    print("\n🧹 清理旧的构建目录...")
    
    dirs_to_clean = [BUILD_DIR, DIST_DIR]
    
    for dir_path in dirs_to_clean:
        if os.path.exists(dir_path):
            try:
                shutil.rmtree(dir_path)
                print(f"  ✅ 已删除: {dir_path}")
            except Exception as e:
                print(f"  ❌ 删除失败 {dir_path}: {e}")
                return False
    
    # 创建新的构建目录
    for dir_path in dirs_to_clean:
        try:
            os.makedirs(dir_path, exist_ok=True)
            print(f"  ✅ 已创建: {dir_path}")
        except Exception as e:
            print(f"  ❌ 创建失败 {dir_path}: {e}")
            return False
    
    return True


def encrypt_with_pyarmor():
    """使用PyArmor加密代码"""
    print("\n🔐 开始使用PyArmor加密代码...")
    
    success_count = 0
    total_count = len(MODULES_TO_ENCRYPT)
    
    for module in MODULES_TO_ENCRYPT:
        module_path = os.path.join(ROOT_DIR, module)
        
        if not os.path.exists(module_path):
            print(f"  ⚠️  文件不存在，跳过: {module}")
            continue
        
        print(f"  🔐 加密文件: {module}")
        
        # 构建加密命令
        cmd = [
            "pyarmor", "gen", 
            "-O", BUILD_DIR,
            module_path
        ]
        
        # 执行加密命令 - 修复编码问题
        try:
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                cwd=ROOT_DIR,
                encoding='utf-8',
                errors='ignore'
            )
            if result.returncode != 0:
                print(f"    ❌ 加密失败: {result.stderr if result.stderr else '未知错误'}")
                print(f"    命令: {' '.join(cmd)}")
                return False
            else:
                print(f"    ✅ 加密成功")
                success_count += 1
        except Exception as e:
            print(f"    ❌ 加密异常: {e}")
            # 尝试简单执行不捕获输出
            try:
                result = subprocess.run(cmd, cwd=ROOT_DIR)
                if result.returncode == 0:
                    print(f"    ✅ 加密成功（fallback模式）")
                    success_count += 1
                else:
                    print(f"    ❌ 加密失败，返回码: {result.returncode}")
                    return False
            except Exception as e2:
                print(f"    ❌ 加密完全失败: {e2}")
                return False
    
    print(f"\n✅ 加密完成: {success_count}/{total_count} 个文件成功")
    return success_count > 0


def copy_resources():
    """复制资源文件到构建目录"""
    print("\n📁 复制资源文件...")
    
    copied_count = 0
    
    # 复制必需资源
    for resource in RESOURCES_TO_COPY:
        src_path = os.path.join(ROOT_DIR, resource)
        dst_path = os.path.join(BUILD_DIR, resource)
        
        if os.path.exists(src_path):
            try:
                if os.path.isdir(src_path):
                    if os.path.exists(dst_path):
                        shutil.rmtree(dst_path)
                    shutil.copytree(src_path, dst_path)
                    print(f"  ✅ 已复制目录: {resource}")
                else:
                    shutil.copy2(src_path, dst_path)
                    print(f"  ✅ 已复制文件: {resource}")
                copied_count += 1
            except Exception as e:
                print(f"  ❌ 复制失败 {resource}: {e}")
        else:
            print(f"  ⚠️  资源不存在，跳过: {resource}")
    
    # 复制可选资源
    for resource in OPTIONAL_RESOURCES:
        src_path = os.path.join(ROOT_DIR, resource)
        dst_path = os.path.join(BUILD_DIR, resource)
        
        if os.path.exists(src_path):
            try:
                if os.path.isdir(src_path):
                    if os.path.exists(dst_path):
                        shutil.rmtree(dst_path)
                    shutil.copytree(src_path, dst_path)
                    print(f"  ✅ 已复制可选目录: {resource}")
                else:
                    shutil.copy2(src_path, dst_path)
                    print(f"  ✅ 已复制可选文件: {resource}")
                copied_count += 1
            except Exception as e:
                print(f"  ⚠️  复制可选资源失败 {resource}: {e}")
        else:
            print(f"  ℹ️  可选资源不存在: {resource}")
    
    print(f"\n✅ 资源复制完成: {copied_count} 个文件/目录")
    return True


def create_spec_file(upx_available=False):
    """创建PyInstaller规格文件"""
    print("\n📄 创建PyInstaller规格文件...")
    
    spec_path = os.path.join(BUILD_DIR, "license_manager.spec")
    
    # 根据UPX可用性调整模板
    spec_content = SPEC_TEMPLATE.replace("upx=True,", f"upx={upx_available},")
    
    try:
        with open(spec_path, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        print(f"  ✅ 规格文件已创建: {spec_path}")
        if upx_available:
            print(f"  ✅ UPX压缩已启用")
        else:
            print(f"  ⚠️  UPX压缩已禁用")
        return spec_path
    except Exception as e:
        print(f"  ❌ 创建规格文件失败: {e}")
        return None


def build_with_pyinstaller():
    """使用PyInstaller打包加密后的代码"""
    print("\n📦 使用PyInstaller打包加密后的代码...")
    
    # 检查UPX是否可用
    upx_available = False
    try:
        upx_result = subprocess.run(['upx', '--version'], capture_output=True, text=True)
        if upx_result.returncode == 0:
            upx_available = True
            print("  ✅ 检测到UPX，将启用压缩")
        else:
            print("  ⚠️  UPX不可用，将禁用压缩")
    except FileNotFoundError:
        print("  ⚠️  UPX未安装，将禁用压缩")
    
    # 创建规格文件（根据UPX可用性调整）
    spec_path = create_spec_file(upx_available)
    if not spec_path:
        return False
    
    # 切换到构建目录
    original_cwd = os.getcwd()
    os.chdir(BUILD_DIR)
    
    try:
        # 构建PyInstaller命令（使用.spec文件时不能加额外参数）
        cmd = ["pyinstaller", "--distpath", DIST_DIR, spec_path]
        
        print(f"  🔧 执行命令: {' '.join(cmd)}")
        print("  ⏳ 打包中，请稍候...")
        
        # 执行打包命令 - 修复中文路径编码问题
        try:
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                encoding='utf-8',
                errors='ignore'  # 忽略编码错误
            )
        except Exception as e:
            print(f"  ❌ 执行命令异常: {e}")
            # 尝试不捕获输出的方式
            try:
                result = subprocess.run(cmd)
                if result.returncode == 0:
                    print("  ✅ PyInstaller打包成功")
                    return True
                else:
                    print(f"  ❌ PyInstaller打包失败，返回码: {result.returncode}")
                    return False
            except Exception as e2:
                print(f"  ❌ 命令执行失败: {e2}")
                return False
        
        if result.returncode != 0:
            print(f"  ❌ PyInstaller打包失败:")
            if hasattr(result, 'stderr') and result.stderr:
                print(f"     标准错误: {result.stderr}")
            if hasattr(result, 'stdout') and result.stdout:
                print(f"     标准输出: {result.stdout}")
            return False
        else:
            print("  ✅ PyInstaller打包成功")
            return True
            
    except Exception as e:
        print(f"  ❌ 打包过程异常: {e}")
        return False
    finally:
        # 恢复工作目录
        os.chdir(original_cwd)


def create_startup_script():
    """创建启动脚本"""
    print("\n📝 创建优化启动脚本...")
    
    # 静默启动的批处理文件 - 完全不显示任何窗口
    bat_content = '''@echo off
cd /d "%~dp0"
if exist "许可证管理器.exe" (
    start /b "" "许可证管理器.exe"
) else (
    exit /b 1
)
'''
    
    # 创建VBS脚本进行完全静默启动 - 推荐方式
    vbs_content = '''Set objShell = CreateObject("WScript.Shell")
objShell.CurrentDirectory = CreateObject("Scripting.FileSystemObject").GetParentFolderName(WScript.ScriptFullName)
If CreateObject("Scripting.FileSystemObject").FileExists("许可证管理器.exe") Then
    objShell.Run """许可证管理器.exe""", 0, False
End If
'''
    
    try:
        # 创建批处理启动脚本
        bat_path = os.path.join(DIST_DIR, "启动许可证管理器.bat")
        with open(bat_path, 'w', encoding='gbk') as f:
            f.write(bat_content)
        print(f"  ✅ 批处理启动脚本: {bat_path}")
        
        # 创建VBS静默启动脚本（推荐）
        vbs_path = os.path.join(DIST_DIR, "静默启动许可证管理器.vbs")
        with open(vbs_path, 'w', encoding='utf-8') as f:
            f.write(vbs_content)
        print(f"  ✅ VBS静默启动脚本: {vbs_path}")
        
    except Exception as e:
        print(f"  ❌ 创建启动脚本失败: {e}")
    
    return True


def generate_build_info():
    """生成构建信息文件"""
    print("\n📋 生成构建信息...")
    
    build_info = f"""许可证管理系统 - 构建信息
===============================

构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
构建版本: v2.0.1 (终端窗口优化版)
加密工具: PyArmor
打包工具: PyInstaller

已加密的文件:
{chr(10).join([f"  - {module}" for module in MODULES_TO_ENCRYPT])}

包含的资源:
{chr(10).join([f"  - {resource}" for resource in RESOURCES_TO_COPY + OPTIONAL_RESOURCES])}

启动方式说明:
1. 直接启动: 双击"许可证管理器.exe"
2. 批处理启动: 双击"启动许可证管理器.bat"
3. 静默启动: 双击"静默启动许可证管理器.vbs" (强烈推荐)

版本优化内容:
✅ 完全禁用控制台窗口弹出
✅ 优化PyInstaller配置参数
✅ 增强引导程序信号处理
✅ 禁用窗口化回溯
✅ 禁用参数模拟
✅ 提供VBS静默启动脚本

解决的问题:
- 反复弹出终端窗口
- 启动时控制台闪现
- GUI应用显示命令行

推荐使用方式:
建议用户使用"静默启动许可证管理器.vbs"脚本启动程序，
这样可以确保完全不显示任何命令行窗口，直接打开GUI界面。

注意事项:
- 请保持文件完整性，不要删除任何文件
- 数据库文件包含重要的许可证信息
- 建议定期备份整个目录
- VBS脚本提供最佳的用户体验

技术支持:
如有问题，请联系开发团队
"""
    
    try:
        info_path = os.path.join(DIST_DIR, "构建信息.txt")
        with open(info_path, 'w', encoding='utf-8') as f:
            f.write(build_info)
        print(f"  ✅ 构建信息文件: {info_path}")
        return True
    except Exception as e:
        print(f"  ❌ 生成构建信息失败: {e}")
        return False


def verify_build():
    """验证构建结果"""
    print("\n🔍 验证构建结果...")
    
    # 检查可执行文件
    exe_path = os.path.join(DIST_DIR, "许可证管理器.exe")
    if os.path.exists(exe_path):
        size_mb = os.path.getsize(exe_path) / (1024 * 1024)
        print(f"  ✅ 可执行文件: {exe_path} ({size_mb:.1f} MB)")
    else:
        print(f"  ❌ 可执行文件不存在: {exe_path}")
        return False
    
    # 检查启动脚本
    expected_files = [
        "启动许可证管理器.bat", 
        "静默启动许可证管理器.vbs",
        "构建信息.txt"
    ]
    
    for filename in expected_files:
        filepath = os.path.join(DIST_DIR, filename)
        if os.path.exists(filepath):
            print(f"  ✅ 文件存在: {filename}")
        else:
            print(f"  ⚠️  文件不存在: {filename}")
    
    return True


def main():
    """主函数"""
    print_banner()
    
    try:
        # 1. 检查依赖
        if not check_dependencies():
            print("\n❌ 依赖检查失败，请安装必要的工具后重试")
            return 1
        
        # 2. 清理目录
        if not clean_directories():
            print("\n❌ 清理目录失败")
            return 1
        
        # 3. 加密代码
        if not encrypt_with_pyarmor():
            print("\n❌ 代码加密失败")
            return 1
        
        # 4. 复制资源
        if not copy_resources():
            print("\n❌ 资源复制失败")
            return 1
        
        # 5. 打包应用
        if not build_with_pyinstaller():
            print("\n❌ 应用打包失败")
            return 1
        
        # 6. 创建启动脚本
        create_startup_script()
        
        # 7. 生成构建信息
        generate_build_info()
        
        # 8. 验证构建结果
        verify_build()
        
        # 构建完成
        print("\n" + "=" * 80)
        print("🎉 优化构建完成！")
        print("=" * 80)
        print(f"✅ 加密后的代码位于: {BUILD_DIR}")
        print(f"✅ 打包后的应用位于: {DIST_DIR}")
        print("\n📁 发布文件：")
        print(f"   - 主程序: {os.path.join(DIST_DIR, '许可证管理器.exe')}")
        print(f"   - 批处理启动: {os.path.join(DIST_DIR, '启动许可证管理器.bat')}")
        print(f"   - VBS静默启动: {os.path.join(DIST_DIR, '静默启动许可证管理器.vbs')} (推荐)")
        print(f"   - 构建信息: {os.path.join(DIST_DIR, '构建信息.txt')}")
        
        print("\n🚀 终端窗口问题解决方案：")
        print("   1. 直接双击exe文件启动")
        print("   2. 使用VBS脚本静默启动（推荐）")
        print("   3. VBS脚本确保不会弹出任何终端窗口")
        
        # 计算总耗时
        end_time = datetime.now()
        print(f"\n⏱️  完成时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("\n✨ 优化版本已生成，终端窗口弹出问题已彻底解决！")
        print("=" * 80)
        
        return 0
        
    except KeyboardInterrupt:
        print("\n\n⚠️  构建被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 构建过程发生异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    
    # 等待用户输入（在Windows环境下方便查看结果）
    if exit_code == 0:
        input("\n按回车键退出...")
    
    sys.exit(exit_code)
