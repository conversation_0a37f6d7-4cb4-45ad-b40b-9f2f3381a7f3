#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
番茄小说上传工具 - 简化打包脚本
使用PyInstaller直接打包（不加密）

适用于快速构建和测试
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

# 项目根目录
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))

# 输出目录
DIST_DIR = os.path.join(ROOT_DIR, "dist_simple")


def clean_directories():
    """清理分发目录"""
    print("清理旧的分发目录...")
    if os.path.exists(DIST_DIR):
        shutil.rmtree(DIST_DIR)
    
    os.makedirs(DIST_DIR, exist_ok=True)


def check_pyinstaller():
    """检查PyInstaller是否安装"""
    print("检查PyInstaller安装状态...")
    try:
        result = subprocess.run(["pyinstaller", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"PyInstaller版本: {result.stdout.strip()}")
            return True
        else:
            print("PyInstaller未正确安装")
            return False
    except FileNotFoundError:
        print("未找到PyInstaller，请先安装: pip install pyinstaller")
        return False


def build_with_pyinstaller():
    """使用PyInstaller直接打包"""
    print("使用PyInstaller打包应用程序...")
    
    main_py = os.path.join(ROOT_DIR, "main.py")
    if not os.path.exists(main_py):
        print("错误: 未找到main.py文件")
        sys.exit(1)
    
    # 检查图标文件是否存在
    icon_path = os.path.join(ROOT_DIR, "logo.ico")
    
    # PyInstaller命令参数 - 打包成单文件
    cmd = [
        "pyinstaller",
        "--name=FanqieBookUpload",
        "--onefile",  # 打包成单个exe文件
        "--windowed",  # GUI应用，不显示控制台
        "--clean",  # 清理临时文件
        "--distpath", DIST_DIR,
        f"--add-data=drivers{os.pathsep}drivers",  # 添加驱动文件夹
        f"--add-data=config.json{os.pathsep}.",  # 添加配置文件
        f"--add-data=src{os.pathsep}src",  # 添加src模块
        "--hidden-import=PySide6.QtCore",
        "--hidden-import=PySide6.QtWidgets", 
        "--hidden-import=PySide6.QtGui",
        "--hidden-import=selenium",
        "--hidden-import=selenium.webdriver",
        "--hidden-import=selenium.webdriver.chrome",
        "--hidden-import=selenium.webdriver.chrome.service",
        "--hidden-import=selenium.webdriver.chrome.options",
        "--hidden-import=selenium.webdriver.common.by",
        "--hidden-import=selenium.webdriver.support.ui",
        "--hidden-import=selenium.webdriver.support.expected_conditions",
        "--hidden-import=selenium.common.exceptions",
        "--hidden-import=src",
        "--hidden-import=src.core",
        "--hidden-import=src.core.fanqie_uploader",
        "--hidden-import=src.core.novel_parser",
        "--hidden-import=src.core.webdriver_manager",
        "--hidden-import=src.core.config_manager",
        "--hidden-import=src.ui",
        "--hidden-import=src.ui.main_window",
        "--hidden-import=src.ui.book_info_widget",
        "--noupx",  # 禁用UPX压缩，避免某些杀毒软件误报
    ]
    
    # 添加图标支持
    if os.path.exists(icon_path):
        cmd.append(f"--icon={icon_path}")
        print(f"使用图标文件: {icon_path}")
    else:
        print("未找到logo.ico文件，将使用默认图标")
    
    # 添加主程序文件
    cmd.append(main_py)
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=ROOT_DIR)
        
        if result.returncode != 0:
            print(f"PyInstaller打包失败: {result.stderr}")
            print(f"标准输出: {result.stdout}")
            sys.exit(1)
        else:
            print("PyInstaller打包成功")
            if result.stdout:
                print(f"标准输出: {result.stdout}")
                
    except Exception as e:
        print(f"执行PyInstaller时出现错误: {str(e)}")
        sys.exit(1)


def copy_additional_files():
    """复制额外的必要文件到分发目录（单文件模式）"""
    print("复制文档文件到分发目录...")
    
    # 单文件模式下，exe直接在DIST_DIR中
    if not os.path.exists(DIST_DIR):
        print("警告: 分发目录不存在")
        return
    
    # 复制文档文件到exe同级目录
    docs_to_copy = [
        "cookie_guide.md", 
        "chrome_automation_guide.md",
    ]
    
    for doc in docs_to_copy:
        src_path = os.path.join(ROOT_DIR, doc)
        if os.path.exists(src_path):
            dst_path = os.path.join(DIST_DIR, doc)
            shutil.copy2(src_path, dst_path)
            print(f"已复制文档: {doc}")


def create_readme():
    """创建部署说明文件（单文件模式）"""
    print("创建部署说明文件...")
    
    if not os.path.exists(DIST_DIR):
        return
    
    readme_content = '''# 番茄小说上传工具 - 单文件版部署说明

## 系统要求
- Windows 10/11 (64位)
- Google Chrome 浏览器（最新版本）

## 使用方法
1. 确保已安装 Google Chrome 浏览器
2. 直接双击运行 "FanqieBookUpload.exe" 即可启动程序

## 使用说明
1. 首次使用请参考 "cookie_guide.md" 获取Cookie信息
2. 按照 "chrome_automation_guide.md" 配置浏览器环境
3. 准备好txt格式的小说文件
4. 在程序中输入Cookie和书本ID
5. 选择小说文件并开始上传

## 注意事项
- 请确保网络连接稳定
- 不要在上传过程中关闭浏览器窗口
- 如遇到弹窗，请按照程序提示手动处理
- 建议在使用前备份重要数据

## 故障排除
- 如果程序无法启动，请检查是否安装了Chrome浏览器
- 如果上传失败，请检查Cookie是否过期
- 如果遇到其他问题，请查看程序日志或联系技术支持

## 优势
- 单文件exe，无需安装，即下即用
- 包含所有依赖，不需要额外环境配置
- 内置ChromeDriver，自动适配Chrome版本

## 版本信息
- 版本: 1.0 (单文件版)
- 构建时间: ''' + str(subprocess.run(['date', '/t'], capture_output=True, text=True, shell=True).stdout.strip()) + '''

祝您使用愉快！
'''
    
    readme_path = os.path.join(DIST_DIR, "使用说明.txt")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"已创建使用说明文件: {readme_path}")


def main():
    """主函数"""
    print("=== 番茄小说上传工具 - 简化打包 ===")
    
    # 检查依赖
    if not check_pyinstaller():
        print("请先安装PyInstaller: pip install pyinstaller")
        sys.exit(1)
    
    try:
        # 清理目录
        clean_directories()
        
        # 直接打包
        build_with_pyinstaller()
        
        # 复制文档文件
        copy_additional_files()
        
        # 创建使用说明
        create_readme()
        
        print("=== 单文件打包完成 ===")
        print(f"打包后的应用位于: {DIST_DIR}")
        print("")
        print("单文件部署说明:")
        print("1. 直接复制 'FanqieBookUpload.exe' 到目标计算机")
        print("2. 确保目标计算机已安装Chrome浏览器")
        print("3. 双击运行 'FanqieBookUpload.exe' 即可启动")
        print("4. 参考相关文档获取Cookie信息")
        
    except Exception as e:
        print(f"构建过程中出现错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
