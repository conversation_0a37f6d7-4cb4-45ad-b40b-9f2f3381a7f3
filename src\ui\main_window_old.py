"""
主窗口界面
"""
import os
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QTextEdit, QFileDialog,
    QTableWidget, QTableWidgetItem, QProgressBar, QMessageBox,
    QGroupBox, QSplitter, QHeaderView
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QIcon
from typing import List, Dict

from ..core.novel_parser import NovelParser
from ..core.fanqie_uploader import FanqieUploader


class UploadThread(QThread):
    """上传线程"""
    
    progress_signal = Signal(str)  # 进度信号
    status_signal = Signal(int, str, str)  # 状态信号 (章节索引, 状态, 章节标题)
    finished_signal = Signal(dict)  # 完成信号
    
    def __init__(self, uploader: FanqieUploader, chapters: List[Dict]):
        super().__init__()
        self.uploader = uploader
        self.chapters = chapters
        
    def run(self):
        """运行上传任务"""
        result = self.uploader.upload_chapters(
            self.chapters,
            progress_callback=self.progress_signal.emit,
            status_callback=self.status_signal.emit
        )
        self.finished_signal.emit(result)


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.novel_parser = NovelParser()
        self.uploader = None
        self.chapters = []
        self.upload_thread = None
        
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("番茄小说自动上传工具 v1.0")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(splitter)
        
        # 创建配置区域
        config_group = self.create_config_group()
        splitter.addWidget(config_group)
        
        # 创建文件处理区域
        file_group = self.create_file_group()
        splitter.addWidget(file_group)
        
        # 创建章节列表区域
        chapter_group = self.create_chapter_group()
        splitter.addWidget(chapter_group)
        
        # 创建日志区域
        log_group = self.create_log_group()
        splitter.addWidget(log_group)
        
        # 设置分割器比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 1)
        splitter.setStretchFactor(2, 3)
        splitter.setStretchFactor(3, 2)
        
    def create_config_group(self) -> QGroupBox:
        """创建配置区域"""
        group = QGroupBox("配置信息")
        layout = QGridLayout(group)
        
        # Cookie输入
        layout.addWidget(QLabel("Cookie:"), 0, 0)
        self.cookie_edit = QLineEdit()
        self.cookie_edit.setPlaceholderText("请输入从浏览器开发者工具获取的Cookie信息")
        layout.addWidget(self.cookie_edit, 0, 1)
        
        self.validate_cookie_btn = QPushButton("验证Cookie")
        self.validate_cookie_btn.clicked.connect(self.validate_cookie)
        layout.addWidget(self.validate_cookie_btn, 0, 2)
        
        # 书本ID输入
        layout.addWidget(QLabel("书本ID:"), 1, 0)
        self.book_id_edit = QLineEdit()
        self.book_id_edit.setPlaceholderText("请输入小说书本ID")
        layout.addWidget(self.book_id_edit, 1, 1)
        
        self.get_book_info_btn = QPushButton("获取书本信息")
        self.get_book_info_btn.clicked.connect(self.get_book_info)
        layout.addWidget(self.get_book_info_btn, 1, 2)
        
        return group
        
    def create_file_group(self) -> QGroupBox:
        """创建文件处理区域"""
        group = QGroupBox("文件处理")
        layout = QHBoxLayout(group)
        
        # 文件选择
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("请选择小说txt文件")
        self.file_path_edit.setReadOnly(True)
        layout.addWidget(self.file_path_edit)
        
        self.select_file_btn = QPushButton("选择文件")
        self.select_file_btn.clicked.connect(self.select_file)
        layout.addWidget(self.select_file_btn)
        
        self.parse_file_btn = QPushButton("解析文件")
        self.parse_file_btn.clicked.connect(self.parse_file)
        self.parse_file_btn.setEnabled(False)
        layout.addWidget(self.parse_file_btn)
        
        return group
        
    def create_chapter_group(self) -> QGroupBox:
        """创建章节列表区域"""
        group = QGroupBox("章节列表")
        layout = QVBoxLayout(group)
        
        # 创建表格
        self.chapter_table = QTableWidget()
        self.chapter_table.setColumnCount(6)
        self.chapter_table.setHorizontalHeaderLabels(["章节标题", "章节数", "章节名称", "字数", "上传状态", "操作"])
        
        # 设置表格属性
        header = self.chapter_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        
        layout.addWidget(self.chapter_table)
        
        # 创建操作按钮区域
        button_layout = QHBoxLayout()
        
        self.upload_all_btn = QPushButton("开始上传")
        self.upload_all_btn.clicked.connect(self.start_upload)
        self.upload_all_btn.setEnabled(False)
        button_layout.addWidget(self.upload_all_btn)
        
        self.stop_upload_btn = QPushButton("停止上传")
        self.stop_upload_btn.clicked.connect(self.stop_upload)
        self.stop_upload_btn.setEnabled(False)
        button_layout.addWidget(self.stop_upload_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        button_layout.addWidget(self.progress_bar)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        return group
        
    def create_log_group(self) -> QGroupBox:
        """创建日志区域"""
        group = QGroupBox("运行日志")
        layout = QVBoxLayout(group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        layout.addWidget(self.log_text)
        
        # 日志操作按钮
        log_button_layout = QHBoxLayout()
        
        clear_log_btn = QPushButton("清空日志")
        clear_log_btn.clicked.connect(self.clear_log)
        log_button_layout.addWidget(clear_log_btn)
        
        save_log_btn = QPushButton("保存日志")
        save_log_btn.clicked.connect(self.save_log)
        log_button_layout.addWidget(save_log_btn)
        
        log_button_layout.addStretch()
        layout.addLayout(log_button_layout)
        
        return group
        
    def log_message(self, message: str):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        
    def select_file(self):
        """选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择小说txt文件", "", "文本文件 (*.txt)"
        )
        
        if file_path:
            self.file_path_edit.setText(file_path)
            self.parse_file_btn.setEnabled(True)
            self.log_message(f"已选择文件: {os.path.basename(file_path)}")
            
    def parse_file(self):
        """解析文件"""
        file_path = self.file_path_edit.text()
        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "警告", "请先选择有效的文件")
            return
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 验证格式
            is_valid, message = self.novel_parser.validate_format(content)
            if not is_valid:
                QMessageBox.critical(self, "格式错误", message)
                self.log_message(f"文件格式验证失败: {message}")
                return
                
            # 解析章节
            novel_info = self.novel_parser.get_novel_info(content)
            self.chapters = novel_info['chapters']
            
            # 更新章节表格
            self.update_chapter_table()
            
            self.log_message(f"文件解析成功: {novel_info['total_chapters']} 个章节，共 {novel_info['total_words']} 字")
            
            # 启用上传按钮
            if self.chapters and self.cookie_edit.text() and self.book_id_edit.text():
                self.upload_all_btn.setEnabled(True)
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"文件解析失败: {str(e)}")
            self.log_message(f"文件解析失败: {str(e)}")
            
    def update_chapter_table(self):
        """更新章节表格"""
        self.chapter_table.setRowCount(len(self.chapters))
        
        for i, chapter in enumerate(self.chapters):
            # 章节标题（原始标题）
            title_item = QTableWidgetItem(chapter['title'])
            self.chapter_table.setItem(i, 0, title_item)
            
            # 章节数
            chapter_number = chapter.get('chapter_number')
            if chapter_number is not None:
                number_item = QTableWidgetItem(str(chapter_number))
            else:
                number_item = QTableWidgetItem("N/A")
            self.chapter_table.setItem(i, 1, number_item)
            
            # 章节名称
            chapter_name = chapter.get('chapter_name', '未知')
            name_item = QTableWidgetItem(chapter_name)
            self.chapter_table.setItem(i, 2, name_item)
            
            # 字数
            word_count_item = QTableWidgetItem(str(chapter['word_count']))
            self.chapter_table.setItem(i, 3, word_count_item)
            
            # 上传状态
            status_item = QTableWidgetItem("待上传")
            self.chapter_table.setItem(i, 4, status_item)
            
            # 操作按钮（暂时留空）
            action_item = QTableWidgetItem("")
            self.chapter_table.setItem(i, 5, action_item)
            
    def validate_cookie(self):
        """验证Cookie"""
        cookie = self.cookie_edit.text().strip()
        if not cookie:
            QMessageBox.warning(self, "警告", "请输入Cookie信息")
            return
            
        self.log_message("正在验证Cookie，启动浏览器...")
        
        # 临时创建上传器进行验证
        temp_uploader = FanqieUploader(cookie, "test")
        success, message = temp_uploader.validate_cookie()
        temp_uploader.close()  # 关闭浏览器
        
        if success:
            QMessageBox.information(self, "成功", message)
            self.log_message(f"Cookie验证成功: {message}")
        else:
            QMessageBox.critical(self, "验证失败", message)
            self.log_message(f"Cookie验证失败: {message}")
        
    def get_book_info(self):
        """获取书本信息"""
        book_id = self.book_id_edit.text().strip()
        cookie = self.cookie_edit.text().strip()
        
        if not book_id:
            QMessageBox.warning(self, "警告", "请输入书本ID")
            return
            
        if not cookie:
            QMessageBox.warning(self, "警告", "请先输入Cookie信息")
            return
            
        self.log_message(f"正在获取书本信息: {book_id}，启动浏览器...")
        
        # 创建临时上传器获取书本信息
        temp_uploader = FanqieUploader(cookie, book_id)
        
        # 先验证Cookie
        success, message = temp_uploader.validate_cookie()
        if not success:
            temp_uploader.close()
            QMessageBox.critical(self, "验证失败", message)
            self.log_message(f"Cookie验证失败: {message}")
            return
        
        # 获取书本信息
        success, book_info = temp_uploader.get_book_info()
        temp_uploader.close()  # 关闭浏览器
        
        if success:
            # 构建详细的书本信息显示
            info_lines = [
                f"📚 书本信息获取成功",
                f"",
                f"🆔 书号: {book_info['book_id']}",
                f"📖 书名: {book_info['title']}",
                f"👥 目标读者: {book_info['target_audience']}",
                f"🏷️ 标签: {book_info['tags']}",
                f"👤 主角: {book_info['main_character']}",
                f"📅 创建时间: {book_info['creation_time']}",
                f"🔒 安全状态: {book_info['security_status']}",
                f"📝 签约状态: {book_info['contract_status']}",
                f"🔄 更新状态: {book_info['update_status']}",
                f"",
                f"📝 简介: {book_info['description'][:100]}{'...' if len(book_info['description']) > 100 else ''}"
            ]
            
            info_text = "\n".join(info_lines)
            QMessageBox.information(self, "书本信息", info_text)
            self.log_message(f"书本信息获取成功: {book_info['title']} ({book_info['update_status']})")
        else:
            QMessageBox.critical(self, "获取失败", f"获取书本信息失败: {book_info.get('error', '未知错误')}")
            self.log_message(f"书本信息获取失败: {book_info.get('error', '未知错误')}")
        
    def start_upload(self):
        """开始上传"""
        if not self.chapters:
            QMessageBox.warning(self, "警告", "请先解析小说文件")
            return
            
        cookie = self.cookie_edit.text().strip()
        book_id = self.book_id_edit.text().strip()
        
        if not cookie or not book_id:
            QMessageBox.warning(self, "警告", "请完善Cookie和书本ID信息")
            return
            
        # 创建上传器并验证Cookie
        self.uploader = FanqieUploader(cookie, book_id)
        
        self.log_message("正在初始化浏览器和验证Cookie...")
        
        # 先验证Cookie，建立浏览器连接
        success, message = self.uploader.validate_cookie()
        if not success:
            self.uploader.close()
            QMessageBox.critical(self, "验证失败", f"Cookie验证失败: {message}")
            self.log_message(f"Cookie验证失败: {message}")
            return
        
        self.log_message("Cookie验证成功，开始上传...")
        
        # 创建并启动上传线程
        self.upload_thread = UploadThread(self.uploader, self.chapters)
        self.upload_thread.progress_signal.connect(self.log_message)
        self.upload_thread.status_signal.connect(self.update_chapter_status)
        self.upload_thread.finished_signal.connect(self.upload_finished)
        
        # 更新UI状态
        self.upload_all_btn.setEnabled(False)
        self.stop_upload_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(len(self.chapters))
        self.progress_bar.setValue(0)
        
        self.log_message("开始上传章节...")
        self.upload_thread.start()
        
    def stop_upload(self):
        """停止上传"""
        if self.upload_thread and self.upload_thread.isRunning():
            self.upload_thread.terminate()
            self.upload_thread.wait()
            
        self.upload_all_btn.setEnabled(True)
        self.stop_upload_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        self.log_message("上传已停止")
        
    def update_chapter_status(self, chapter_index: int, status: str, title: str):
        """更新章节状态"""
        status_map = {
            'uploading': '上传中...',
            'success': '上传成功',
            'failed': '上传失败'
        }
        
        status_text = status_map.get(status, status)
        status_item = QTableWidgetItem(status_text)
        self.chapter_table.setItem(chapter_index, 4, status_item)  # 状态列现在是第4列（索引4）
        
        if status in ['success', 'failed']:
            self.progress_bar.setValue(self.progress_bar.value() + 1)
            
    def upload_finished(self, result: dict):
        """上传完成"""
        self.upload_all_btn.setEnabled(True)
        self.stop_upload_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        # 显示上传结果
        message = f"上传完成！\n总计：{result['total']} 个章节\n成功：{result['success']} 个\n失败：{result['failed']} 个"
        
        if result['failed'] > 0:
            QMessageBox.warning(self, "上传完成", message)
        else:
            QMessageBox.information(self, "上传成功", message)
            
        self.log_message(f"上传任务完成 - 成功: {result['success']}, 失败: {result['failed']}")
        
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        
    def save_log(self):
        """保存日志"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存日志", "upload_log.txt", "文本文件 (*.txt)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                QMessageBox.information(self, "成功", "日志保存成功")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"日志保存失败: {str(e)}")
