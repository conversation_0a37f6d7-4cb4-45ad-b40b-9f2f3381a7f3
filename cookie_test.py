"""
Cookie测试和调试工具
"""
import sys
from src.core.fanqie_uploader import FanqieUploader

def test_cookie_format(cookie_string):
    """测试Cookie格式"""
    print("=== Cookie格式分析 ===")
    print(f"Cookie长度: {len(cookie_string)} 字符")
    
    if not cookie_string.strip():
        print("❌ Cookie为空")
        return False
    
    # 分析Cookie结构
    cookies = cookie_string.split(';')
    print(f"Cookie项目数: {len(cookies)}")
    
    valid_cookies = 0
    for i, cookie in enumerate(cookies):
        cookie = cookie.strip()
        if '=' in cookie and cookie:
            name, value = cookie.split('=', 1)
            name = name.strip()
            value = value.strip()
            if name and value:
                print(f"  ✅ Cookie {i+1}: {name} = {value[:20]}...")
                valid_cookies += 1
            else:
                print(f"  ❌ Cookie {i+1}: 名称或值为空")
        else:
            print(f"  ❌ Cookie {i+1}: 格式错误 - {cookie[:50]}")
    
    print(f"有效Cookie数量: {valid_cookies}/{len(cookies)}")
    
    if valid_cookies == 0:
        print("❌ 没有有效的Cookie")
        return False
    
    print("✅ Cookie格式基本正确")
    return True

def test_cookie_login(cookie_string, book_id="test"):
    """测试Cookie登录"""
    print("\n=== Cookie登录测试 ===")
    
    try:
        uploader = FanqieUploader(cookie_string, book_id)
        success, message = uploader.validate_cookie()
        uploader.close()
        
        if success:
            print("✅ Cookie验证成功")
            print(f"消息: {message}")
            return True
        else:
            print("❌ Cookie验证失败")
            print(f"错误: {message}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        return False

def main():
    print("番茄小说Cookie测试工具")
    print("=" * 40)
    
    # 获取用户输入的Cookie
    print("请粘贴您的Cookie字符串:")
    print("(可以从浏览器开发者工具的Network标签页中复制)")
    print()
    
    cookie_string = input("Cookie: ").strip()
    
    if not cookie_string:
        print("❌ 未输入Cookie")
        return
    
    # 测试Cookie格式
    format_ok = test_cookie_format(cookie_string)
    
    if not format_ok:
        print("\n建议:")
        print("1. 确保从浏览器开发者工具正确复制了完整的Cookie")
        print("2. Cookie应该包含多个key=value对，用分号分隔")
        print("3. 示例格式: sessionid=abc123; csrftoken=def456; userId=789")
        return
    
    # 测试Cookie登录
    login_ok = test_cookie_login(cookie_string)
    
    if login_ok:
        print("\n🎉 Cookie测试通过！可以使用主程序了。")
    else:
        print("\n故障排除建议:")
        print("1. 重新登录番茄小说作家工作台")
        print("2. 获取最新的Cookie信息")
        print("3. 确保Cookie包含sessionid、csrftoken等关键字段")
        print("4. 检查网络连接是否正常")

if __name__ == "__main__":
    main()
