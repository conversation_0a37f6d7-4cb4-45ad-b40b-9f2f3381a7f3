"""
书本信息展示组件 - 无封面版本
"""
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QGroupBox, QGridLayout, QScrollArea, QFrame
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont
from typing import Dict

class BookInfoWidget(QWidget):
    """书本信息展示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.book_info = {}
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建组框
        self.group_box = QGroupBox("书本信息")
        self.group_box.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 创建内容组件（只有信息区域，无封面）
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        
        # 创建信息区域
        self.create_info_area(content_layout)
        
        scroll_area.setWidget(content_widget)
        
        # 设置组框布局
        group_layout = QVBoxLayout(self.group_box)
        group_layout.addWidget(scroll_area)
        
        layout.addWidget(self.group_box)
        
        # 初始显示空状态
        self.show_empty_state()
        
    def create_info_area(self, parent_layout):
        """创建信息区域"""
        info_frame = QFrame()
        info_layout = QVBoxLayout(info_frame)
        
        # 创建信息网格
        info_grid = QGridLayout()
        info_grid.setSpacing(10)
        
        # 定义信息字段（移除cover_url）
        self.info_fields = {
            'title': '书名',
            'book_id': '书号', 
            'target_audience': '目标读者',
            'tags': '标签',
            'main_character': '主角',
            'creation_time': '创建时间',
            'security_status': '安全状态',
            'contract_status': '签约状态',
            'update_status': '更新状态'
        }
        
        self.info_labels = {}
        
        row = 0
        for field, display_name in self.info_fields.items():
            # 字段名称标签
            name_label = QLabel(f"{display_name}:")
            name_label.setStyleSheet("font-weight: bold; color: #333;")
            name_label.setFixedWidth(80)
            
            # 字段值标签
            value_label = QLabel("未知")
            value_label.setStyleSheet("color: #666; padding: 2px;")
            value_label.setWordWrap(True)
            
            info_grid.addWidget(name_label, row, 0)
            info_grid.addWidget(value_label, row, 1)
            
            self.info_labels[field] = value_label
            row += 1
        
        # 简介区域（独占一行）
        desc_label = QLabel("简介:")
        desc_label.setStyleSheet("font-weight: bold; color: #333;")
        
        self.description_label = QLabel("暂无简介")
        self.description_label.setStyleSheet("""
            QLabel {
                color: #666;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #fafafa;
            }
        """)
        self.description_label.setWordWrap(True)
        self.description_label.setMinimumHeight(80)
        
        info_layout.addLayout(info_grid)
        info_layout.addWidget(desc_label)
        info_layout.addWidget(self.description_label)
        info_layout.addStretch()
        
        parent_layout.addWidget(info_frame)
        
    def show_empty_state(self):
        """显示空状态"""
        for label in self.info_labels.values():
            label.setText("未知")
            
        self.description_label.setText("暂无简介")
        
    def update_book_info(self, book_info: Dict):
        """更新书本信息"""
        self.book_info = book_info
        
        # 更新基本信息
        for field, label in self.info_labels.items():
            value = book_info.get(field, "未知")
            if value:
                label.setText(str(value))
            else:
                label.setText("未知")
        
        # 更新简介
        description = book_info.get('description', '')
        if description:
            self.description_label.setText(description)
        else:
            self.description_label.setText("暂无简介")
    
    def clear_info(self):
        """清除信息"""
        self.show_empty_state()
        self.book_info = {}
    
    def get_book_info(self) -> Dict:
        """获取当前书本信息"""
        return self.book_info.copy()
