#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
番茄小说上传工具 - PyArmor打包脚本
用于加密Python代码并使用PyInstaller打包

此脚本执行以下操作：
1. 使用PyArmor加密项目代码
2. 使用PyInstaller打包加密后的代码为可执行文件
3. 复制必要的资源文件和配置文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

# 项目根目录
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))

# 输出目录
BUILD_DIR = os.path.join(ROOT_DIR, "build_encrypted")
DIST_DIR = os.path.join(ROOT_DIR, "dist_encrypted")

# 需要加密的模块
MODULES_TO_ENCRYPT = [
    "src/core",
    "src/ui", 
    "src/__init__.py",
    "main.py"
]

# 需要复制的资源文件和配置文件
RESOURCES_TO_COPY = [
    "drivers",
    "config.json",
    "requirements.txt",
    "README.md",
    "cookie_guide.md",
    "chrome_automation_guide.md",
    "logo.ico"
]

# 需要复制但不加密的Python文件
COPY_ONLY_FILES = [
    # 这些文件可能包含测试代码或示例，直接复制即可
]


def clean_directories():
    """清理构建和分发目录"""
    print("清理旧的构建目录...")
    if os.path.exists(BUILD_DIR):
        shutil.rmtree(BUILD_DIR)
    if os.path.exists(DIST_DIR):
        shutil.rmtree(DIST_DIR)
    
    # 创建新的构建目录
    os.makedirs(BUILD_DIR, exist_ok=True)
    os.makedirs(DIST_DIR, exist_ok=True)


def check_pyarmor():
    """检查PyArmor是否安装"""
    print("检查PyArmor安装状态...")
    try:
        result = subprocess.run(["pyarmor", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"PyArmor版本: {result.stdout.strip()}")
            return True
        else:
            print("PyArmor未正确安装")
            return False
    except FileNotFoundError:
        print("未找到PyArmor，请先安装: pip install pyarmor")
        return False


def check_pyinstaller():
    """检查PyInstaller是否安装"""
    print("检查PyInstaller安装状态...")
    try:
        result = subprocess.run(["pyinstaller", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"PyInstaller版本: {result.stdout.strip()}")
            return True
        else:
            print("PyInstaller未正确安装")
            return False
    except FileNotFoundError:
        print("未找到PyInstaller，请先安装: pip install pyinstaller")
        return False


def encrypt_with_pyarmor():
    """使用PyArmor加密代码"""
    print("开始使用PyArmor加密代码...")
    
    # 加密主模块和各个包
    for module in MODULES_TO_ENCRYPT:
        module_path = os.path.join(ROOT_DIR, module)
        if not os.path.exists(module_path):
            print(f"警告: 模块 {module} 不存在，跳过")
            continue
            
        if os.path.isdir(module_path):
            # 如果是目录，递归加密
            print(f"加密目录: {module}")
            # 修复PyArmor输出路径，避免嵌套目录
            if module == "src/core":
                output_dir = os.path.join(BUILD_DIR, "src", "core")
            elif module == "src/ui":
                output_dir = os.path.join(BUILD_DIR, "src", "ui")
            else:
                output_dir = os.path.join(BUILD_DIR, module)
            
            # 确保输出目录的父目录存在
            os.makedirs(os.path.dirname(output_dir), exist_ok=True)
            
            cmd = [
                "pyarmor", "gen", 
                "-O", output_dir,
                "-r", 
                "--platform", "windows.x86_64",
                module_path
            ]
        else:
            # 如果是文件，直接加密
            print(f"加密文件: {module}")
            # 为单个文件创建正确的输出路径
            if module == "main.py":
                output_dir = BUILD_DIR
            else:
                output_dir = os.path.join(BUILD_DIR, os.path.dirname(module))
                os.makedirs(output_dir, exist_ok=True)
            
            cmd = [
                "pyarmor", "gen", 
                "-O", output_dir,
                "--platform", "windows.x86_64",
                module_path
            ]
        
        # 执行加密命令
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"加密 {module} 失败: {result.stderr}")
            print(f"命令: {' '.join(cmd)}")
            sys.exit(1)
        else:
            print(f"加密 {module} 成功")


def fix_nested_directories():
    """修复PyArmor创建的嵌套目录结构"""
    print("修复嵌套目录结构...")
    
    # 修复ui目录
    ui_nested = os.path.join(BUILD_DIR, "src", "ui", "ui")
    if os.path.exists(ui_nested):
        ui_temp = os.path.join(BUILD_DIR, "src", "ui_temp")
        shutil.move(ui_nested, ui_temp)
        shutil.rmtree(os.path.join(BUILD_DIR, "src", "ui"))
        shutil.move(ui_temp, os.path.join(BUILD_DIR, "src", "ui"))
        print("已修复ui目录结构")
    
    # 修复core目录
    core_nested = os.path.join(BUILD_DIR, "src", "core", "core")
    if os.path.exists(core_nested):
        core_temp = os.path.join(BUILD_DIR, "src", "core_temp")
        shutil.move(core_nested, core_temp)
        shutil.rmtree(os.path.join(BUILD_DIR, "src", "core"))
        shutil.move(core_temp, os.path.join(BUILD_DIR, "src", "core"))
        print("已修复core目录结构")


def copy_resources():
    """复制资源文件到构建目录"""
    print("复制资源文件...")
    for resource in RESOURCES_TO_COPY:
        src_path = os.path.join(ROOT_DIR, resource)
        dst_path = os.path.join(BUILD_DIR, resource)
        
        if not os.path.exists(src_path):
            print(f"警告: 资源 {resource} 不存在，跳过")
            continue
            
        if os.path.isdir(src_path):
            # 如果是目录，复制整个目录
            if os.path.exists(dst_path):
                shutil.rmtree(dst_path)
            shutil.copytree(src_path, dst_path)
            print(f"已复制目录: {resource}")
        elif os.path.isfile(src_path):
            # 如果是文件，直接复制
            os.makedirs(os.path.dirname(dst_path), exist_ok=True)
            shutil.copy2(src_path, dst_path)
            print(f"已复制文件: {resource}")


def create_spec_file():
    """创建PyInstaller的spec文件 - 单文件版本"""
    print("创建PyInstaller spec文件（单文件模式）...")
    
    # 检查图标文件是否存在
    icon_path = os.path.join(ROOT_DIR, "logo.ico")
    icon_spec = f"'{icon_path}'" if os.path.exists(icon_path) else "None"
    if os.path.exists(icon_path):
        print(f"找到图标文件: {icon_path}")
    else:
        print("未找到logo.ico文件，将使用默认图标")
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('drivers', 'drivers'),
        ('config.json', '.'),
        ('src', 'src'),
    ],
    hiddenimports=[
        'PySide6.QtCore',
        'PySide6.QtWidgets', 
        'PySide6.QtGui',
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.chrome',
        'selenium.webdriver.chrome.service',
        'selenium.webdriver.chrome.options',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support.expected_conditions',
        'selenium.common.exceptions',
        'requests',
        'requests.adapters',
        'requests.auth',
        'requests.cookies',
        'requests.exceptions',
        'requests.models',
        'requests.sessions',
        'requests.structures',
        'requests.utils',
        'urllib3',
        'urllib3.poolmanager',
        'urllib3.util',
        'urllib3.util.retry',
        'json',
        'time',
        'os',
        'sys',
        'pathlib',
        'zipfile',
        'tempfile',
        'shutil',
        'platform',
        'configparser',
        'src',
        'src.core',
        'src.core.fanqie_uploader',
        'src.core.novel_parser',
        'src.core.webdriver_manager',
        'src.core.config_manager',
        'src.ui',
        'src.ui.main_window',
        'src.ui.book_info_widget',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='FanqieBookUpload',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # 禁用UPX压缩，避免某些杀毒软件误报
    upx_exclude=[],
    console=False,  # 设置为False创建GUI应用
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon={icon_spec},  # 动态设置图标路径
)
'''
    
    spec_path = os.path.join(BUILD_DIR, "main.spec")
    with open(spec_path, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print(f"已创建spec文件: {spec_path}")
    return spec_path


def build_with_pyinstaller():
    """使用PyInstaller打包加密后的代码"""
    print("使用PyInstaller打包加密后的代码...")
    
    # 创建spec文件
    spec_path = create_spec_file()
    
    # 切换到构建目录
    original_cwd = os.getcwd()
    os.chdir(BUILD_DIR)
    
    try:
        # 不使用UPX压缩，避免与PyArmor冲突和编码问题
        cmd = ["pyinstaller", "--distpath", DIST_DIR, "--clean", "main.spec"]
        print("不使用UPX压缩（避免兼容性问题）")
        
        print(f"执行命令: {' '.join(cmd)}")
        
        # 修复编码问题：使用UTF-8编码并忽略错误
        try:
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                encoding='utf-8',
                errors='ignore',
                timeout=300  # 设置5分钟超时
            )
        except subprocess.TimeoutExpired:
            print("PyInstaller执行超时，可能遇到问题")
            sys.exit(1)
        except UnicodeDecodeError:
            # 如果UTF-8编码失败，尝试使用系统默认编码
            print("尝试使用系统默认编码...")
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True,
                errors='ignore'
            )
        
        if result.returncode != 0:
            print(f"PyInstaller打包失败:")
            if result.stderr:
                print(f"错误输出: {result.stderr}")
            if result.stdout:
                print(f"标准输出: {result.stdout}")
            sys.exit(1)
        else:
            print("PyInstaller打包成功")
            if result.stdout:
                # 只显示关键输出信息，避免编码问题
                lines = result.stdout.split('\n')
                for line in lines:
                    if any(keyword in line.lower() for keyword in ['warning', 'error', 'successfully', 'completed']):
                        print(f"  {line}")
            
    except Exception as e:
        print(f"PyInstaller执行过程中出现异常: {str(e)}")
        sys.exit(1)
    finally:
        # 恢复原始工作目录
        os.chdir(original_cwd)


def copy_additional_files():
    """复制额外的必要文件到分发目录（单文件模式）"""
    print("复制文档文件到分发目录...")
    
    # 单文件模式下，exe直接在DIST_DIR中
    if not os.path.exists(DIST_DIR):
        print("警告: 分发目录不存在")
        return
    
    # 复制文档文件到exe同级目录
    docs_to_copy = [
        "cookie_guide.md", 
        "chrome_automation_guide.md",
    ]
    
    for doc in docs_to_copy:
        src_path = os.path.join(ROOT_DIR, doc)
        if os.path.exists(src_path):
            dst_path = os.path.join(DIST_DIR, doc)
            shutil.copy2(src_path, dst_path)
            print(f"已复制文档: {doc}")


def create_readme():
    """创建部署说明文件（单文件模式）"""
    print("创建部署说明文件...")
    
    if not os.path.exists(DIST_DIR):
        return
    
    readme_content = '''# 番茄小说上传工具 - 单文件版部署说明（加密版）

## 系统要求
- Windows 10/11 (64位)
- Google Chrome 浏览器（最新版本）

## 使用方法
1. 确保已安装 Google Chrome 浏览器
2. 直接双击运行 "FanqieBookUpload.exe" 即可启动程序

## 使用说明
1. 首次使用请参考 "cookie_guide.md" 获取Cookie信息
2. 按照 "chrome_automation_guide.md" 配置浏览器环境
3. 准备好txt格式的小说文件
4. 在程序中输入Cookie和书本ID
5. 选择小说文件并开始上传

## 注意事项
- 请确保网络连接稳定
- 不要在上传过程中关闭浏览器窗口
- 如遇到弹窗，请按照程序提示手动处理
- 建议在使用前备份重要数据

## 故障排除
- 如果程序无法启动，请检查是否安装了Chrome浏览器
- 如果上传失败，请检查Cookie是否过期
- 如果遇到其他问题，请查看程序日志或联系技术支持

## 优势
- 单文件exe，无需安装，即下即用
- 包含所有依赖，不需要额外环境配置
- 内置ChromeDriver，自动适配Chrome版本
- 源代码经过PyArmor加密保护

## 版本信息
- 版本: 1.0 (单文件加密版)
- 构建时间: ''' + str(subprocess.run(['date', '/t'], capture_output=True, text=True, shell=True).stdout.strip()) + '''

祝您使用愉快！
'''
    
    readme_path = os.path.join(DIST_DIR, "使用说明.txt")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"已创建使用说明文件: {readme_path}")


def main():
    """主函数"""
    print("=== 番茄小说上传工具 - 开始构建加密的应用程序 ===")
    
    # 检查依赖
    if not check_pyarmor():
        print("请先安装PyArmor: pip install pyarmor")
        sys.exit(1)
    
    if not check_pyinstaller():
        print("请先安装PyInstaller: pip install pyinstaller")
        sys.exit(1)
    
    try:
        # 清理目录
        clean_directories()
        
        # 加密代码
        encrypt_with_pyarmor()
        
        # 修复嵌套目录结构
        fix_nested_directories()
        
        # 复制资源
        copy_resources()
        
        # 打包应用
        build_with_pyinstaller()
        
        # 复制文档文件
        copy_additional_files()
        
        # 创建使用说明
        create_readme()
        
        print("=== 单文件加密打包完成 ===")
        print(f"加密后的代码位于: {BUILD_DIR}")
        print(f"打包后的应用位于: {DIST_DIR}")
        print("")
        print("单文件部署说明:")
        print("1. 直接复制 'FanqieBookUpload.exe' 到目标计算机")
        print("2. 确保目标计算机已安装Chrome浏览器")
        print("3. 双击运行 'FanqieBookUpload.exe' 即可启动")
        print("4. 源代码已通过PyArmor加密保护")
        
    except Exception as e:
        print(f"构建过程中出现错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
