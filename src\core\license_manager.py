#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
许可证管理器
用于验证机器码和许可证密钥
"""

import os
import sys
import json
import hashlib
import hmac
import base64
import platform
import uuid
import subprocess
from datetime import datetime
from typing import Tuple, Dict, Optional


class LicenseManager:
    """许可证管理器"""
    
    def __init__(self):
        self.secret_key = "FanqieBookUpload2024SecretKey"  # 与生成器保持一致
        self.license_file = "license.json"
        self.cached_license = None
        self.cached_machine_code = None  # 缓存机器码，避免重复生成
        
    def get_machine_info(self) -> Optional[Dict]:
        """获取机器硬件信息"""
        try:
            info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'machine': platform.machine(),
                'node': platform.node(),
                'system': platform.system(),
                'version': platform.version(),
                'mac_address': ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                                        for elements in range(0, 2*6, 2)][::-1]),
            }
            
            # Windows系统获取更多信息
            if platform.system() == "Windows":
                try:
                    # 设置subprocess参数，隐藏命令行窗口
                    startupinfo = None
                    if hasattr(subprocess, 'STARTUPINFO'):
                        startupinfo = subprocess.STARTUPINFO()
                        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                        startupinfo.wShowWindow = subprocess.SW_HIDE

                    # 获取主板序列号
                    result = subprocess.run(['wmic', 'baseboard', 'get', 'serialnumber'],
                                          capture_output=True, text=True,
                                          startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        if len(lines) > 1:
                            info['motherboard_serial'] = lines[1].strip()

                    # 获取硬盘序列号
                    result = subprocess.run(['wmic', 'diskdrive', 'get', 'serialnumber'],
                                          capture_output=True, text=True,
                                          startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        if len(lines) > 1:
                            info['disk_serial'] = lines[1].strip()

                    # 获取CPU信息
                    result = subprocess.run(['wmic', 'cpu', 'get', 'processorid'],
                                          capture_output=True, text=True,
                                          startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        if len(lines) > 1:
                            info['cpu_id'] = lines[1].strip()
                except:
                    pass
            
            return info
            
        except Exception as e:
            print(f"获取机器信息失败: {e}")
            return None
    
    def generate_machine_code(self, machine_info=None, use_cache=True) -> Optional[str]:
        """生成机器码"""
        # 如果启用缓存且已有缓存的机器码，直接返回
        if use_cache and self.cached_machine_code:
            return self.cached_machine_code

        if machine_info is None:
            machine_info = self.get_machine_info()

        if not machine_info:
            return None

        # 将机器信息转换为字符串
        info_str = json.dumps(machine_info, sort_keys=True)

        # 生成机器码（使用SHA256）
        machine_code = hashlib.sha256(info_str.encode('utf-8')).hexdigest()[:16].upper()

        # 缓存机器码
        if use_cache:
            self.cached_machine_code = machine_code

        return machine_code
    
    def verify_license_key(self, license_key: str, machine_code: str) -> Tuple[bool, str]:
        """验证许可证密钥"""
        try:
            # 这里可以添加本地验证逻辑
            # 或者调用远程服务器验证
            
            # 简单的本地验证（实际部署时应该连接服务器）
            if not license_key or len(license_key) < 10:
                return False, "许可证密钥格式无效"
            
            if not machine_code or len(machine_code) != 16:
                return False, "机器码格式无效"
            
            # TODO: 这里应该实现真正的验证逻辑
            # 可以连接到服务器验证，或者使用加密算法验证
            
            return True, "许可证验证成功"
            
        except Exception as e:
            return False, f"验证过程出错: {str(e)}"
    
    def save_license(self, license_key: str, machine_code: str) -> bool:
        """保存许可证信息到本地"""
        try:
            license_data = {
                'license_key': license_key,
                'machine_code': machine_code,
                'save_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'verified': True
            }

            with open(self.license_file, 'w', encoding='utf-8') as f:
                json.dump(license_data, f, ensure_ascii=False, indent=2)

            # 更新缓存
            self.cached_license = license_data
            self.cached_machine_code = machine_code  # 同时缓存机器码
            return True

        except Exception as e:
            print(f"保存许可证失败: {e}")
            return False
    
    def load_license(self) -> Optional[Dict]:
        """加载本地许可证信息"""
        try:
            if os.path.exists(self.license_file):
                with open(self.license_file, 'r', encoding='utf-8') as f:
                    license_data = json.load(f)
                self.cached_license = license_data
                return license_data
            return None
            
        except Exception as e:
            print(f"加载许可证失败: {e}")
            return None
    
    def is_licensed(self) -> Tuple[bool, str]:
        """检查是否已授权"""
        try:
            # 首先检查缓存
            if self.cached_license:
                license_data = self.cached_license
            else:
                # 加载本地许可证
                license_data = self.load_license()

            if not license_data:
                return False, "未找到许可证文件"

            # 获取保存的机器码
            saved_machine_code = license_data.get('machine_code', '')
            if not saved_machine_code:
                return False, "许可证文件中缺少机器码"

            # 获取当前机器码，优先使用缓存
            current_machine_code = self.generate_machine_code(use_cache=True)
            if not current_machine_code:
                return False, "无法获取机器码"

            # 检查机器码是否匹配
            if current_machine_code != saved_machine_code:
                # 如果不匹配，尝试重新生成机器码（不使用缓存）
                fresh_machine_code = self.generate_machine_code(use_cache=False)
                if fresh_machine_code and fresh_machine_code == saved_machine_code:
                    # 更新缓存
                    self.cached_machine_code = fresh_machine_code
                    current_machine_code = fresh_machine_code
                else:
                    return False, "机器码不匹配，请重新激活"

            # 验证许可证密钥
            license_key = license_data.get('license_key', '')
            is_valid, message = self.verify_license_key(license_key, current_machine_code)

            if is_valid:
                return True, "许可证有效"
            else:
                return False, f"许可证验证失败: {message}"

        except Exception as e:
            return False, f"验证过程出错: {str(e)}"
    
    def activate_license(self, license_key: str) -> Tuple[bool, str]:
        """激活许可证"""
        try:
            # 获取机器码
            machine_code = self.generate_machine_code()
            if not machine_code:
                return False, "无法获取机器码"
            
            # 验证许可证密钥
            is_valid, message = self.verify_license_key(license_key, machine_code)
            if not is_valid:
                return False, f"许可证验证失败: {message}"
            
            # 保存许可证
            if self.save_license(license_key, machine_code):
                return True, "许可证激活成功"
            else:
                return False, "保存许可证失败"
                
        except Exception as e:
            return False, f"激活过程出错: {str(e)}"
    
    def get_machine_code(self) -> Optional[str]:
        """获取当前机器的机器码"""
        return self.generate_machine_code()
    
    def clear_license(self) -> bool:
        """清除本地许可证"""
        try:
            if os.path.exists(self.license_file):
                os.remove(self.license_file)
            # 清除所有缓存
            self.cached_license = None
            self.cached_machine_code = None
            return True
        except Exception as e:
            print(f"清除许可证失败: {e}")
            return False

    def refresh_cache(self):
        """刷新缓存，强制重新加载许可证和重新生成机器码"""
        self.cached_license = None
        self.cached_machine_code = None


class LicenseDialog:
    """许可证激活对话框（用于GUI）"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.license_manager = LicenseManager()
    
    def show_activation_dialog(self):
        """显示激活对话框"""
        from PySide6.QtWidgets import (
            QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
            QLineEdit, QPushButton, QTextEdit, QMessageBox
        )
        from PySide6.QtCore import Qt
        
        dialog = QDialog(self.parent)
        dialog.setWindowTitle("软件激活")
        dialog.setFixedSize(500, 400)
        
        layout = QVBoxLayout(dialog)
        
        # 标题
        title_label = QLabel("番茄小说上传工具 - 软件激活")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 机器码显示
        machine_code = self.license_manager.get_machine_code()
        machine_layout = QHBoxLayout()
        machine_layout.addWidget(QLabel("机器码:"))
        machine_code_edit = QLineEdit(machine_code or "获取失败")
        machine_code_edit.setReadOnly(True)
        machine_layout.addWidget(machine_code_edit)
        
        copy_btn = QPushButton("复制")
        copy_btn.clicked.connect(lambda: self.copy_to_clipboard(machine_code))
        machine_layout.addWidget(copy_btn)
        layout.addLayout(machine_layout)
        
        # 许可证密钥输入
        license_layout = QHBoxLayout()
        license_layout.addWidget(QLabel("许可证密钥:"))
        license_key_edit = QLineEdit()
        license_key_edit.setPlaceholderText("请输入许可证密钥")
        license_layout.addWidget(license_key_edit)
        layout.addLayout(license_layout)
        
        # 说明文本
        info_text = QTextEdit()
        info_text.setPlainText(
            "激活说明：\n\n"
            "1. 复制上方显示的机器码\n"
            "2. 联系软件提供商获取许可证密钥\n"
            "3. 将许可证密钥粘贴到输入框中\n"
            "4. 点击激活按钮完成激活\n\n"
            "注意：\n"
            "• 每个许可证密钥只能在指定的机器上使用\n"
            "• 如果更换硬件，可能需要重新激活\n"
            "• 请保管好您的许可证密钥"
        )
        info_text.setReadOnly(True)
        info_text.setMaximumHeight(150)
        layout.addWidget(info_text)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        activate_btn = QPushButton("激活")
        activate_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        def activate():
            license_key = license_key_edit.text().strip()
            if not license_key:
                QMessageBox.warning(dialog, "警告", "请输入许可证密钥")
                return
            
            success, message = self.license_manager.activate_license(license_key)
            if success:
                QMessageBox.information(dialog, "成功", message)
                dialog.accept()
            else:
                QMessageBox.critical(dialog, "激活失败", message)
        
        activate_btn.clicked.connect(activate)
        button_layout.addWidget(activate_btn)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(dialog.reject)
        button_layout.addWidget(cancel_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        return dialog.exec() == QDialog.Accepted
    
    def copy_to_clipboard(self, text):
        """复制到剪贴板"""
        try:
            from PySide6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(text)
        except Exception as e:
            print(f"复制失败: {e}")


def check_license_on_startup():
    """程序启动时检查许可证"""
    license_manager = LicenseManager()
    is_valid, message = license_manager.is_licensed()
    
    if not is_valid:
        print(f"许可证检查失败: {message}")
        return False
    
    print("许可证验证成功")
    return True


if __name__ == "__main__":
    # 测试代码
    license_manager = LicenseManager()
    
    print("机器码:", license_manager.get_machine_code())
    
    is_valid, message = license_manager.is_licensed()
    print(f"许可证状态: {message}")
    
    if not is_valid:
        print("需要激活许可证")
