#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
番茄小说自动上传工具 - 主程序入口
包含许可证验证机制
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.main_window import MainWindow
from src.core.license_manager import LicenseManager, LicenseDialog


def check_license():
    """检查软件许可证"""
    license_manager = LicenseManager()
    
    # 检查是否已授权
    is_valid, message = license_manager.is_licensed()
    
    if is_valid:
        print(f"许可证验证成功: {message}")
        return True
    
    print(f"许可证验证失败: {message}")
    
    # 显示激活对话框
    license_dialog = LicenseDialog()
    
    # 显示提示信息
    reply = QMessageBox.question(
        None,
        "软件激活",
        f"软件需要激活才能使用。\n\n"
        f"错误信息: {message}\n\n"
        f"是否现在激活？",
        QMessageBox.Yes | QMessageBox.No,
        QMessageBox.Yes
    )
    
    if reply == QMessageBox.Yes:
        # 显示激活对话框
        if license_dialog.show_activation_dialog():
            # 激活成功，使用同一个license_manager实例重新检查
            # 这样可以利用缓存，避免重复生成机器码导致的不匹配问题
            is_valid, message = license_manager.is_licensed()
            if is_valid:
                QMessageBox.information(None, "激活成功", "软件激活成功，即将启动程序。")
                return True
            else:
                # 如果验证失败，刷新缓存后重试一次
                license_manager.refresh_cache()
                is_valid_retry, message_retry = license_manager.is_licensed()
                if is_valid_retry:
                    QMessageBox.information(None, "激活成功", "软件激活成功，即将启动程序。")
                    return True
                else:
                    QMessageBox.critical(None, "激活失败", f"激活后验证失败: {message_retry}")
                    return False
        else:
            QMessageBox.information(None, "取消激活", "您取消了激活，程序将退出。")
            return False
    else:
        QMessageBox.information(None, "程序退出", "软件需要激活后才能使用。")
        return False


def main():
    """主函数"""
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("番茄小说自动上传工具")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("FanqieBookUpload")
    
    # 设置应用程序图标
    icon_path = os.path.join(os.path.dirname(__file__), 'logo.ico')
    if os.path.exists(icon_path):
        app.setWindowIcon(QIcon(icon_path))
    
    # 检查许可证
    if not check_license():
        print("许可证验证失败，程序退出")
        sys.exit(1)
    
    # 创建主窗口
    try:
        window = MainWindow()
        window.show()
        
        print("程序启动成功")
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except Exception as e:
        QMessageBox.critical(None, "启动错误", f"程序启动时发生错误: {str(e)}")
        print(f"程序启动错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
