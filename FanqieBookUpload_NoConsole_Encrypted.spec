# -*- mode: python ; coding: utf-8 -*-

import sys
import os

# 加密后的项目目录
ENCRYPTED_DIR = r"Z:\Python\FanqieBookUpload\encrypted_temp"

a = Analysis(
    [r"Z:\Python\FanqieBookUpload\encrypted_temp\main.py"],
    pathex=[str(ENCRYPTED_DIR)],
    binaries=[],
    datas=[
        (r"Z:\Python\FanqieBookUpload\encrypted_temp\drivers", 'drivers'),
        (r"Z:\Python\FanqieBookUpload\encrypted_temp\config.json", '.'),
        (r"Z:\Python\FanqieBookUpload\encrypted_temp\src", 'src'),
        (r"Z:\Python\FanqieBookUpload\encrypted_temp\logo.ico", '.'),
        (r"Z:\Python\FanqieBookUpload\encrypted_temp\pyarmor_runtime_000000", 'pyarmor_runtime_000000'),
    ],
    hiddenimports=[
        'PySide6.QtCore',
        'PySide6.QtWidgets', 
        'PySide6.QtGui',
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.chrome',
        'selenium.webdriver.chrome.service',
        'selenium.webdriver.chrome.options',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support.expected_conditions',
        'selenium.common.exceptions',
        'src',
        'src.core',
        'src.core.fanqie_uploader',
        'src.core.novel_parser',
        'src.core.webdriver_manager',
        'src.core.config_manager',
        'src.core.license_manager',
        'src.ui',
        'src.ui.main_window',
        'src.ui.book_info_widget',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='FanqieBookUpload',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # 禁用UPX压缩
    upx_exclude=[],
    console=False,  # 关键：设置为False创建无控制台GUI应用
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=r"Z:\Python\FanqieBookUpload\encrypted_temp\logo.ico",
    # Windows特定选项
    version_file=None,
    uac_admin=False,
    uac_uiaccess=False,
)
