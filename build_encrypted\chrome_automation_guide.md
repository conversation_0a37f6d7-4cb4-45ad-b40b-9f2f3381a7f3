# Chrome 浏览器自动化上传指南

## 功能说明

本工具现在使用 Chrome 浏览器自动化技术来实现真实的章节上传功能。程序会：

1. 自动启动 Chrome 浏览器
2. 设置用户提供的 Cookie 进行登录
3. 访问番茄小说作家工作台
4. 自动填写章节标题和内容
5. 保存到草稿箱

## 使用步骤

### 1. 准备 Chrome 浏览器

确保已安装 Google Chrome 浏览器的最新版本。

### 2. 获取 Cookie

**重要：Cookie 是登录验证的关键信息**

1. 打开 Chrome 浏览器
2. 访问 https://fanqienovel.com
3. 登录你的作家账号
4. 按 F12 打开开发者工具
5. 切换到"Network"标签页
6. 刷新页面
7. 找到任意一个请求，点击查看详情
8. 在请求头中找到"Cookie"字段
9. 复制完整的 Cookie 字符串

**Cookie 示例格式：**

```
sessionid=abc123; csrftoken=def456; userId=789; ...
```

### 3. 获取书本 ID

1. 在作家工作台中进入你要上传的小说管理页面
2. 查看浏览器地址栏的 URL
3. 找到类似这样的数字：`/book/7508306478608960536`
4. 复制这串数字作为书本 ID

### 4. 运行程序

```bash
python main.py
```

### 5. 配置信息

1. **输入 Cookie**：粘贴刚才复制的完整 Cookie 字符串
2. **验证 Cookie**：点击"验证 Cookie"按钮，程序会启动浏览器验证登录状态
3. **输入书本 ID**：输入你的小说书本 ID
4. **获取书本信息**：点击按钮验证书本 ID 是否正确

### 6. 上传章节

1. **选择文件**：选择格式正确的 txt 小说文件
2. **解析文件**：点击解析按钮提取章节信息
3. **开始上传**：点击"开始上传"，程序会自动：
   - 启动 Chrome 浏览器
   - 设置 Cookie 登录
   - 逐个访问章节创建页面
   - 自动填写标题和内容
   - 保存到草稿箱

## 技术特点

### Chrome WebDriver 自动化

- 使用 Selenium WebDriver 控制真实的 Chrome 浏览器
- 模拟真实用户的填表操作
- 支持动态网页和 JavaScript 渲染
- 绕过简单的反爬虫检测

### 智能元素定位

程序会自动尝试多种选择器来定位页面元素：

- 标题输入框：`input[placeholder*='标题']`, `input[name*='title']`
- 内容编辑器：`textarea[placeholder*='内容']`, `div[contenteditable='true']`
- 保存按钮：包含"保存草稿"文字的按钮

### 错误处理

- 网络超时处理
- 元素定位失败重试
- 详细的错误日志记录
- 浏览器资源自动清理

## 注意事项

### 1. 浏览器兼容性

- 需要 Chrome 浏览器（推荐最新版本）
- 程序会自动下载匹配的 ChromeDriver
- 如果自动下载失败，需要手动安装 ChromeDriver

### 2. Cookie 安全

- Cookie 包含敏感的登录信息，请妥善保管
- 不要在公共场所或他人面前输入 Cookie
- Cookie 有时效性，过期后需要重新获取

### 3. 上传频率

- 程序会在每个章节之间自动延迟 3 秒
- 避免过于频繁的请求导致账号异常
- 建议在网络状况良好时进行上传

### 4. 浏览器窗口

- 上传过程中会看到 Chrome 浏览器窗口自动操作
- 请不要手动关闭浏览器窗口
- 不要在上传过程中操作浏览器

## 故障排除

### 1. ChromeDriver 问题

**错误信息：** `chromedriver executable needs to be in PATH`

**解决方案：**

- 更新到 Selenium 4.6+版本，支持自动管理
- 或手动下载 ChromeDriver 并添加到系统 PATH

### 2. Cookie 验证失败

**可能原因：**

- Cookie 已过期
- Cookie 格式不正确
- 网络连接问题

**解决方案：**

- 重新登录番茄小说并获取新的 Cookie
- 检查 Cookie 是否完整复制
- 确认网络连接正常

### 3. 元素定位失败

**可能原因：**

- 番茄小说网站更新了页面结构
- 网络加载缓慢

**解决方案：**

- 检查网络连接
- 等待页面完全加载后重试
- 联系开发者更新元素选择器

### 4. 上传中断

**处理方式：**

- 程序会记录已上传的章节
- 可以重新启动继续未完成的上传
- 检查日志了解中断原因

## 开发说明

如果需要适配网站变化，可以修改以下文件：

- `src/core/fanqie_uploader.py` - 主要的自动化逻辑
- 元素选择器在各个方法中的`selectors`数组中定义
- 可以根据实际网页结构调整选择器

## 更新日志

- v1.0: 基于 requests 的 API 调用（模拟）
- v2.0: 改为 Chrome 浏览器自动化填表操作
- 支持真实的网页交互和动态内容
- 增强了反检测能力
